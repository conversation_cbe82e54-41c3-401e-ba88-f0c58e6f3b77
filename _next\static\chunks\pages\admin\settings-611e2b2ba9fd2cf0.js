(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[577],{4034:function(e,a,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/settings",function(){return s(6006)}])},1946:function(e,a,s){"use strict";s.r(a);var t=s(5893);s(7294);a.default=function(){return(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:"Application URL"}),(0,t.jsx)("div",{className:"m-4",children:(0,t.jsxs)("ul",{className:"list-disc",children:[(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Part 1 : Application Url"}),"Link android and ios app to your website.",(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/application_url/appUrl.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Part 2 : Site Url"}),"site url is the url of your website.",(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/application_url/sitesetting.png",alt:""})]})]})})]})}},4692:function(e,a,s){"use strict";s.r(a);var t=s(5893);s(7294);a.default=function(){return(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:"Email settings"}),(0,t.jsxs)("div",{className:"m-4",children:[(0,t.jsx)("p",{className:"text-[18px] font-semibold",children:"To send email from the system,email credential need to be full-fill . There are three email option to chose from"}),(0,t.jsxs)("p",{className:"text-[18px] font-bold text-green-600",children:["** Select the default email. Email always send from default email **"," "]}),(0,t.jsxs)("ul",{className:"list-disc",children:[(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"SendGrid Smtp"}),"sendgrid is a cloud-based email service that provides reliable transactional and marketing email delivery. It is a great option for sending emails from your application. It is a great option for sending emails from your application. Go to"," ",(0,t.jsx)("a",{href:"https://sendgrid.com/",target:"_blank",rel:"noreferrer",className:"text-blue-500",children:"https://sendgrid.com/"})," ","and create an account.",(0,t.jsx)("p",{className:"text-[18px] font-semibold text-green-500",children:"After creating an account, you will get an API key. Copy the API key and paste it in the API key field."}),(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/email/sendgrid.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Gmail Provider"}),"Gmail is a free email service provided by Google. It is a great option for sending emails from your application.",(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/email/gmail.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Other Provider"}),"other provider can be used to send email from the system.",(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/email/other.png",alt:""})]})]})]})]})}},6006:function(e,a,s){"use strict";s.r(a);var t=s(5893),l=(s(7294),s(3652)),i=s(5236),r=s(1946),n=s(1690),c=s(4692),o=s(2411),d=s(6347),m=s(256),x=s(6415),g=s(3318),p=[{title:"App Url",page:(0,t.jsx)(r.default,{})},{title:"Site Settings",page:(0,t.jsx)(n.default,{})},{title:"Emails Settings",page:(0,t.jsx)(c.default,{})},{title:"SMS Settings",page:(0,t.jsx)(o.default,{})},{title:"Whatsapp Settings",page:(0,t.jsx)(m.default,{})},{title:"Push Notification",page:(0,t.jsx)(d.default,{})},{title:"Language Settings",page:(0,t.jsx)(x.default,{})},{title:"Payment Settings",page:(0,t.jsx)(g.default,{})}];a.default=function(){return(0,t.jsx)(l.Z,{children:(0,t.jsx)("div",{className:"bg-yellow-50 bg-opacity-20 h-auto m-6 w-[90%] text-[16px] p-4 rounded",children:(0,t.jsx)(i.Z,{defaultActiveKey:"1",centered:!0,children:p.map((function(e,a){return(0,t.jsx)(i.Z.TabPane,{tab:e.title,children:e.page},a+1)}))})})})}},6415:function(e,a,s){"use strict";s.r(a);var t=s(5893);s(7294);a.default=function(){return(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:"Language Setting"}),"This page is used to set the language of your website.You can see all the available languages and their status. you can add new language and set the default language.You can also delete the language.",(0,t.jsx)("div",{className:"m-4",children:(0,t.jsxs)("ul",{className:"list-disc",children:[(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Part 1 : Language & Translation Page"}),"This page is used to set the language of your website.You can see all the available languages and their status. you can add new language and set the default.Also you can delete the language.",(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/language/1.%20language_translation.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Part 2 : Add Language"}),"To add a new language, click on the add language button. Enter the language name and select the flag. rtl is used for right to left language like Arabic, Persian, Urdu etc.",(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/language/2.add%20language.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Part 3 : Status"}),"Status is used to enable or disable the language.Only the enabled language will be shown in the System.",(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/language/3.status.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Part 4: Default"}),"Only one language can be set as default. When a new user visits the website, the default language will be shown.",(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/language/4.default.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Part 5: Edit & Delete"}),"You can edit or delete the language from here.",(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/language/5.edit_del.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Part 6: Translation"}),"You can translate the language from here. Enter the value in the text box and click on the save button.",(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/language/6.translation.png",alt:""})]})]})})]})}},3318:function(e,a,s){"use strict";s.r(a);var t=s(5893);s(1664),s(7294),s(3652);a.default=function(){return(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:"Payment settings"}),(0,t.jsxs)("div",{className:"m-4",children:["payment settings will be here . First you need to select the payment gateway. Then you need to fill the credential of the payment gateway. we are providing 5 payment gateway option.Choose the payment gateway that you want to use.",(0,t.jsxs)("ul",{className:"list-disc",children:[(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"SSLCOMMERZ"}),(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/payment/ssl.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Stripe"}),(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/payment/stripe.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Paypal"}),(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/payment/paypal.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"RazorPay"}),(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/payment/rz.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Mollie"}),(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/payment/moll.png",alt:""})]})]})]})]})}},1690:function(e,a,s){"use strict";s.r(a);var t=s(5893);s(7294);a.default=function(){return(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:"Site settings"}),(0,t.jsx)("div",{className:"m-4",children:(0,t.jsxs)("ul",{className:"list-disc",children:[(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Part 1 : Site credentials"}),"Site credentials are basic information about the site.They are all self explanatory.",(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/sitesetting/credentials.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Part 2 : Fill Social media links"}),"social medial link are displayed in the footer of the site.",(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/sitesetting/sosial.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Part 2 : Recaptcha credentials"}),"recaptcha credentials are used to prevent spamming of the sign up form.",(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/sitesetting/recaptcha.png",alt:""})]})]})})]})}},2411:function(e,a,s){"use strict";s.r(a);var t=s(5893);s(7294);a.default=function(){return(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:"SMS settings"}),(0,t.jsxs)("div",{className:"m-4",children:[(0,t.jsx)("p",{className:"text-[18px] font-semibold",children:"To send SMS from the system, SMS credential need to be full-fill ."}),(0,t.jsx)("ul",{className:"list-disc",children:(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Twillo Provider"}),"Twillo is a cloud-based email service that provides reliable transactional and marketing email delivery. It is a great option for sending SMS from your application.",(0,t.jsxs)("p",{className:"text-[18px] font-semibold text-green-500",children:["Go to"," ",(0,t.jsx)("a",{href:"https://www.twilio.com/",target:"_blank",rel:"noreferrer",className:"text-blue-500",children:"https://www.twilio.com/"})," ","and create an account."]}),(0,t.jsx)("p",{className:"text-[18px] font-semibold text-green-500",children:"After creating an account, you will get an API key. Copy the API key and paste it in the API key field."}),(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/wa/sms.png",alt:""})]})})]})]})}},256:function(e,a,s){"use strict";s.r(a);var t=s(5893);s(7294);a.default=function(){return(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:"WhatsApp settings"}),(0,t.jsxs)("div",{className:"m-4",children:[(0,t.jsx)("p",{className:"text-[18px] font-semibold",children:"To send whatsapp messages from the system, fill up credential ."}),(0,t.jsx)("ul",{className:"list-disc",children:(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Twillo Provider"}),"Twillo also provides whatsapp service. It is a great option for sending whatsapp messages from your application.",(0,t.jsxs)("p",{className:"text-[18px] font-semibold text-green-500",children:["Go to"," ",(0,t.jsx)("a",{href:"https://www.twilio.com/",target:"_blank",rel:"noreferrer",className:"text-blue-500",children:"https://www.twilio.com/"})," ","and create an account."]}),(0,t.jsx)("p",{className:"text-[18px] font-semibold text-green-500",children:"After creating an account, you will get an API key. Copy the API key and paste it in the API key field."}),(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/wa/wa.png",alt:""})]})})]})]})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,5236,3652,6347,9774,2888,179],(function(){return a=4034,e(e.s=a);var a}));var a=e.O();_N_E=a}]);