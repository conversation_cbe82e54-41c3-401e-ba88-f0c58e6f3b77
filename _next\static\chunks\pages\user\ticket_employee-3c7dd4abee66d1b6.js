(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3329],{5537:function(e,t,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/user/ticket_employee",function(){return s(6404)}])},6404:function(e,t,s){"use strict";s.r(t);var i=s(5893),l=(s(1664),s(7294),s(3652));t.default=function(){return(0,i.jsx)(l.Z,{children:(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:" Employee Panel "}),(0,i.jsx)("div",{className:"m-4",children:(0,i.jsxs)("ul",{className:"list-disc",children:[(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"All ticket"}),(0,i.jsx)("p",{className:"my-4",children:"This is the page where employee can see the total number of tickets, total number of open tickets, total number of closed tickets, total number of pending tickets, total number of tickets assigned to him/her, total number of open tickets assigned to him/her, total number of closed tickets assigned to him/her, total number of pending tickets assigned to him/her."}),(0,i.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/employee/ticket-employee/details.png ",alt:""})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Ticket-details page"}),"Each employee can see the tickets assigned to him/her. He can view the details of the ticket, close the ticket, add notes and reply to the ticket,see the files of the ticket.",(0,i.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/employee/ticket-employee/panel.png",alt:""}),(0,i.jsxs)("ul",{className:"list-disc",children:[(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"1. Replay to ticket"}),"Replay to ticket is marked by number 1 . employee can reply to the ticket by typing the reply in the text area and click on the send button."]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"2. Add notes"}),"Replay to ticket is marked by number 1 . employee can reply to the ticket by typing the reply in the text area and click on the send button.",(0,i.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/employee/ticket-employee/notes.png "})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"3. Ticket Descriptions"}),"Basic information of the ticket is marked by number 3. employee can see the ticket details."]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"4.Ticket Status"}),"Ticket status is marked by number 4. employee can change the status of the ticket.",(0,i.jsx)("p",{className:"text-red-400 font-semibold",children:"**If ticket status is open then only user can reply to the ticket.**"})]})]})]})]})})]})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return t=5537,e(e.s=t);var t}));var t=e.O();_N_E=t}]);