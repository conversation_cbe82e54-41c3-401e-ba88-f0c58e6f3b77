(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1963],{4048:function(e,s,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/accountant/login",function(){return n(151)}])},151:function(e,s,n){"use strict";n.r(s);var a=n(5893),t=(n(7294),n(3652));s.default=function(){return(0,a.jsx)(t.Z,{children:(0,a.jsxs)("div",{className:"bg-gray-50 h-auto m-6 w-[90%] text-[16px] p-4",children:[(0,a.jsxs)("div",{className:"my-4",children:[(0,a.jsx)("h5",{className:"border-b-[1px] border-black",children:"Accountant Sign Up"}),(0,a.jsxs)("div",{className:"m-4",children:[(0,a.jsx)("p",{children:"A new Accountant can register his account through the website sign-up page, but first, he/she needs to check the mark accountant button, then fill up the sign-up information. An email will be sent to the corresponding accountant and admin to complete the sign-up process like the user."}),(0,a.jsx)("img",{className:"h-uto",src:"/images/acsi.png",alt:""})]})]}),(0,a.jsxs)("div",{className:"my-4",children:[(0,a.jsx)("h5",{className:"border-b-[1px] border-black",children:"Accountant Login"}),(0,a.jsxs)("div",{className:"m-4",children:[(0,a.jsx)("p",{children:"After confirmation from the admin side, the administrator will receive the login credential through his/her email. Then, the accountant can log in to the system."}),(0,a.jsx)("img",{className:"h-96",src:"/images/login.png",alt:""})]})]}),(0,a.jsxs)("div",{className:"my-4",children:[(0,a.jsx)("h5",{className:"border-b-[1px] border-black",children:"Accountant dashboard"}),(0,a.jsxs)("div",{className:"m-4",children:[(0,a.jsx)("p",{children:"After the successful login, the accountant will redirect to his/her dashboard."}),(0,a.jsx)("img",{src:"/images/acdash.png",alt:""})]})]}),(0,a.jsx)("div",{className:"border-2 border-red-300 my-4 h-20 text-[18px] font-medium flex items-center justify-center",children:(0,a.jsxs)("p",{className:"mb-0",children:[(0,a.jsx)("span",{className:"text-red-500",children:"*"})," All users, accountants, and admins have the same type of profile page. They can easily manage their personal profile information."]})})]})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return s=4048,e(e.s=s);var s}));var s=e.O();_N_E=s}]);