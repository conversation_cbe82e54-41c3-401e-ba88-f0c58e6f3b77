(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2198],{3039:function(e,n,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/driver-panel/documents",function(){return t(6)}])},3401:function(e,n,t){"use strict";t.d(n,{O:function(){return r}});var a=t(5893),r=(t(7294),function(e){var n=e.heading;return(0,a.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] pb-2",children:n})})},6:function(e,n,t){"use strict";t.r(n);var a=t(5893),r=(t(7294),t(3652)),s=t(3401);n.default=function(){return(0,a.jsx)(r.Z,{children:(0,a.jsxs)("div",{className:"bg-yellow-50 bg-opacity-20 h-auto m-6 w-[90%] text-[16px] p-4 rounded",children:[(0,a.jsx)(s.O,{heading:"Documents"}),(0,a.jsx)("p",{children:"User can view and update the uploaded documents here"}),(0,a.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/driver-panel/documents.png",alt:""}),(0,a.jsxs)("p",{className:"font-bold mt-3",children:["To update the documents click on the ",(0,a.jsx)("strong",{children:"Update Documents"})," button and fill the form with appropriate information and click ",(0,a.jsx)("strong",{children:"Update"})]}),(0,a.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/driver-panel/document-update.png",alt:""})]})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return n=3039,e(e.s=n);var n}));var n=e.O();_N_E=n}]);