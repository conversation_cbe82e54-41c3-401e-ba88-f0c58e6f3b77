(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4523],{9782:function(e,a,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/live/more",function(){return s(2897)}])},2897:function(e,a,s){"use strict";s.r(a);var n=s(5893),t=(s(7294),s(3652));a.default=function(){return(0,n.jsx)(t.Z,{children:(0,n.jsxs)("div",{className:"bg-zinc-100 h-auto m-6 w-[90%] text-[16px] p-4",children:[(0,n.jsx)("h4",{className:"text-center font-semibold underline",children:"More Page"}),(0,n.jsx)("p",{className:"text-center",children:"This section is a little different from the whole site because you can add more pages to your website according to your needs in the admin panel."}),(0,n.jsxs)("div",{className:"my-4",children:[(0,n.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"more"}),(0,n.jsxs)("div",{className:"m-4",children:[(0,n.jsx)("p",{className:"text-base",children:"Here, anyone can see a drop-down menu that has some page names. Business, safety, press and faq's pages ase fixed here. The admin can create unlimited custom pages in the admin panel. The custom page name will display in the more drop-down menu."}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/homepage/more/more-menu.png",alt:""})]})]}),(0,n.jsxs)("div",{className:"my-4",children:[(0,n.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"business"}),(0,n.jsxs)("div",{className:"m-4",children:[(0,n.jsx)("p",{className:"text-base",children:"Here, anyone can see a page title, subtitle, and page content that is fully dynamic. The admin can update all information in the admin panel at any time."}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/homepage/more/business.png",alt:""})]})]}),(0,n.jsxs)("div",{className:"my-4",children:[(0,n.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"safety"}),(0,n.jsxs)("div",{className:"m-4",children:[(0,n.jsx)("p",{className:"text-base",children:"Here, anyone can see a page title, subtitle, and page content with an image that is fully dynamic. The admin can create, update or delete any information in the admin panel at any time."}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/homepage/more/safety.png",alt:""})]})]}),(0,n.jsxs)("div",{className:"my-4",children:[(0,n.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"press"}),(0,n.jsxs)("div",{className:"m-4",children:[(0,n.jsx)("p",{className:"text-base",children:"Here, anyone can see a page title, subtitle, and some press content. The admin can update, create, or delete any information in the admin panel."}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/homepage/more/press.png",alt:""})]})]}),(0,n.jsxs)("div",{className:"my-4",children:[(0,n.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"FAQ's"}),(0,n.jsxs)("div",{className:"m-4",children:[(0,n.jsx)("p",{className:"text-base",children:"Here, anyone can see a page title, subtitle, and faq page content that is fully dynamic. The admin can create, update or delete any faq in the admin panel at any time."}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/homepage/more/faq.png",alt:""})]})]}),(0,n.jsxs)("div",{className:"my-4",children:[(0,n.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"Custom page"}),(0,n.jsxs)("div",{className:"m-4",children:[(0,n.jsx)("p",{className:"text-base",children:"Here, anyone can see a page title, subtitle, and custom page content that is fully dynamic. The admin can create, update or delete any custom page in the admin panel at any time."}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/homepage/more/custom.png",alt:""})]})]})]})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return a=9782,e(e.s=a);var a}));var a=e.O();_N_E=a}]);