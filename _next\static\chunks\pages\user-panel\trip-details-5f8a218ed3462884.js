(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7273],{7642:function(s,e,i){(window.__NEXT_P=window.__NEXT_P||[]).push(["/user-panel/trip-details",function(){return i(6465)}])},3401:function(s,e,i){"use strict";i.d(e,{O:function(){return t}});var a=i(5893),t=(i(7294),function(s){var e=s.heading;return(0,a.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] pb-2",children:e})})},854:function(s,e,i){"use strict";var a=i(5893),t=(i(7294),i(3401));e.Z=function(){return(0,a.jsxs)("div",{className:"bg-yellow-50 bg-opacity-20 h-auto m-6 w-[90%] text-[16px] p-4 rounded",children:[(0,a.jsx)(t.O,{heading:"Trip Details"}),(0,a.jsx)("p",{children:"The user can view detailed information about a trip from here."}),(0,a.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/user-panel/trip-details.png",alt:"trip-details"}),(0,a.jsxs)("ul",{className:"list-disc mt-3 space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("li",{className:"list-disc",children:[(0,a.jsx)("span",{className:"font-semibold",children:"Trip Details section: "}),"The pickup and drop location of the trip with google map"]}),(0,a.jsx)("li",{className:"list-none",children:(0,a.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/user-panel/trip-details-pickup.png",alt:""})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("li",{className:"list-disc",children:[(0,a.jsx)("span",{className:"font-semibold",children:"Rating section: "}),"Rating and comment given by the user. this section might be blank it there is no rating."]}),(0,a.jsx)("li",{className:"list-none",children:(0,a.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/user-panel/rating.png",alt:""})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("li",{className:"list-disc",children:[(0,a.jsx)("span",{className:"font-semibold",children:"Driver Details: "}),"Details of the trip's driver"]}),(0,a.jsx)("li",{className:"list-none",children:(0,a.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/user-panel/driver-details.png",alt:""})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("li",{className:"list-disc",children:[(0,a.jsx)("span",{className:"font-semibold",children:"User Details: "}),"Details of the User"]}),(0,a.jsx)("li",{className:"list-none",children:(0,a.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/user-panel/user-details.png",alt:""})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("li",{className:"list-disc",children:[(0,a.jsx)("span",{className:"font-semibold",children:"Payment Details: "}),"Payment details of the trip. User can split the payment in different methods. In the payment breakdown section, the user can view in which method the user has paid how much money."]}),(0,a.jsx)("li",{className:"list-none",children:(0,a.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/user-panel/payment-details.png",alt:""})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("li",{className:"list-disc",children:[(0,a.jsx)("span",{className:"font-semibold",children:"Vehicle Details: "}),"Details of the vehicle"]}),(0,a.jsx)("li",{className:"list-none",children:(0,a.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/user-panel/vehicle-details.png",alt:""})})]})]})]})}},6465:function(s,e,i){"use strict";i.r(e);var a=i(5893),t=(i(7294),i(3652)),l=i(854);e.default=function(){return(0,a.jsx)(t.Z,{children:(0,a.jsx)(l.Z,{})})}}},function(s){s.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return e=7642,s(s.s=e);var e}));var e=s.O();_N_E=e}]);