(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2471],{1765:function(e,t,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/support-ticket/ticket",function(){return s(3386)}])},3386:function(e,t,s){"use strict";s.r(t);var a=s(5893);s(7294);t.default=function(){return(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:" Support Tickets "}),(0,a.jsx)("div",{className:"m-4",children:(0,a.jsx)("ul",{className:"list-disc",children:(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Detail page"}),"This is the detail page of the ticket. Here you can see the details of the ticket.",(0,a.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/support_ticket/ticket/1ticketpage.png",alt:""}),(0,a.jsxs)("ul",{className:"list-disc",children:[(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Search & Filter"}),"You can search and filter the tickets by using the search bar and filter options.",(0,a.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/support_ticket/ticket/2searchandfilter.png",alt:""})]}),(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Add Ticket"}),"You can add a new ticket by clicking on the add button. This will open a modal where you can add the details of the ticket.",(0,a.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/support_ticket/ticket/3addticket.png",alt:""})]}),(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Ticket Details"}),"You can see the details of the ticket in the detail page. You can also see the Notes and Files of the ticket.",(0,a.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/support_ticket/ticket/4detailspage.png",alt:""})]}),(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:" Details page Functionality"}),"Details page has the following functionality:",(0,a.jsxs)("ul",{className:"list-disc",children:[(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Reply to ticket"}),"You can reply to the ticket . Type your reply in the text area and click on the send button."]}),(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Add Note"}),"You can add a note to the ticket. Click on the add note button and type your note in the text area."]}),(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"CLose a ticket "}),"Only admin & employee can close a ticket. Click on the close button to close the ticket.",(0,a.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/support_ticket/ticket/5details%20page%20funtionality.png",alt:""})]})]})]})]})]})})})]})}}},function(e){e.O(0,[9774,2888,179],(function(){return t=1765,e(e.s=t);var t}));var t=e.O();_N_E=t}]);