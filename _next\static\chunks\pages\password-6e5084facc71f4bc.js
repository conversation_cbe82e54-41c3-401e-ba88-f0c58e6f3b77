(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8322],{8515:function(e,s,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/password",function(){return r(33)}])},33:function(e,s,r){"use strict";r.r(s);var a=r(5893),n=(r(7294),r(3652));s.default=function(){return(0,a.jsx)(n.Z,{children:(0,a.jsxs)("div",{className:"bg-gray-50 h-auto m-6 w-[90%] text-[16px] p-4",children:[(0,a.jsx)("h5",{className:"border-b-[1px] border-black",children:"Password reset option"}),(0,a.jsxs)("div",{className:"m-4",children:[(0,a.jsxs)("p",{children:["If an user forgot the password, click on the ",(0,a.jsx)("span",{className:"text-red-500 font-bold",children:"Forgot Password"})," option. Then you will redirect like the below page. Now enter your registered email ID and click on the button ",(0,a.jsx)("span",{className:"text-red-500 font-bold",children:"Send Password Reset Link"}),"."]}),(0,a.jsx)("img",{className:"w-auto h-auto my-2",src:"/images/reemail.png",alt:""}),(0,a.jsx)("p",{children:"Now you can change your password here."}),(0,a.jsx)("img",{className:"w-auto h-auto my-2",src:"/images/reset.png",alt:""})]})]})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return s=8515,e(e.s=s);var s}));var s=e.O();_N_E=s}]);