(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3768],{942:function(e,t,i){(window.__NEXT_P=window.__NEXT_P||[]).push(["/driver-panel/my-ratings",function(){return i(7432)}])},3401:function(e,t,i){"use strict";i.d(t,{O:function(){return n}});var r=i(5893),n=(i(7294),function(e){var t=e.heading;return(0,r.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] pb-2",children:t})})},7432:function(e,t,i){"use strict";i.r(t);var r=i(5893),n=(i(7294),i(3652)),s=i(3401),a=i(1664),l=i.n(a);t.default=function(){return(0,r.jsx)(n.Z,{children:(0,r.jsxs)("div",{className:"bg-yellow-50 bg-opacity-20 h-auto m-6 w-[90%] text-[16px] p-4 rounded",children:[(0,r.jsx)(s.O,{heading:"My Ratings"}),(0,r.jsx)("p",{children:"Drivers receive ratings from the user when a trip is finished through the app "}),(0,r.jsx)("p",{children:"This section shows:"}),(0,r.jsxs)("ul",{className:"list-disc",children:[(0,r.jsx)("li",{children:"The total number of rating the driver got"}),(0,r.jsx)("li",{children:"Average rating based on all the ratings"}),(0,r.jsx)("li",{children:"A table containing all the ratings earned from different trips"})]}),(0,r.jsxs)("p",{className:"mt-3",children:["Clicking on the Trip Id will redirect the user to the ",(0,r.jsx)(l(),{href:"/trip-details",children:(0,r.jsx)("a",{target:"blank",children:"trip-details"})})," page"]}),(0,r.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/driver-panel/my-ratings.png",alt:""}),(0,r.jsx)("p",{className:"mt-3",children:"Sometimes the comment might be big to fit in the table. In that case, hovering over the comment cell will show a pop over of the entire comment"}),(0,r.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/driver-panel/my-ratings-details.png",alt:""})]})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return t=942,e(e.s=t);var t}));var t=e.O();_N_E=t}]);