(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6382],{5670:function(e,s,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/push-notification/all_notification",function(){return t(7845)}])},7845:function(e,s,t){"use strict";t.r(s);var i=t(5893);t(7294);s.default=function(){return(0,i.jsxs)("div",{children:[(0,i.jsxs)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:["All Push-notifications"," "]}),(0,i.jsx)("div",{className:"m-4",children:(0,i.jsx)("ul",{className:"list-disc",children:(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Descriptions"}),"All the push-notifications that are sent to the users will be displayed in this table.Push-notifications are sorted by the date and time they are sent. Table contains the following columns:",(0,i.jsxs)("ul",{className:"list-disc",children:[(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Title"}),(0,i.jsx)("p",{children:"It is the title of the notification that is sent to the user."})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Body"}),(0,i.jsx)("p",{children:"It is the body of the notification that is sent to the user."})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Status"}),(0,i.jsx)("p",{children:"a notification's status can be either success,scheduled or failed"})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Date"}),(0,i.jsx)("p",{children:"It is the date and time when the notification is sent to the user."})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Group Name"}),(0,i.jsxs)("p",{children:['It is the name of the group to which the notification is sent.if group name showed as "...", that means it is send to all user or driver or users.'," "]})]})]}),(0,i.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/push_notifications/all.png",alt:""})]})})})]})}}},function(e){e.O(0,[9774,2888,179],(function(){return s=5670,e(e.s=s);var s}));var s=e.O();_N_E=s}]);