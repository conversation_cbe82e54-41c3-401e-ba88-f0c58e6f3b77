(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8430],{7259:function(a,e,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/userManagement",function(){return s(30)}])},30:function(a,e,s){"use strict";s.r(e);var t=s(5893),n=(s(7294),s(5236)),i=s(3652),r=s(1163);e.default=function(){(0,r.useRouter)();return(0,t.jsx)(i.Z,{children:(0,t.jsx)("div",{className:"bg-yellow-50 bg-opacity-20 h-auto m-6 w-[90%] text-[16px] p-4 rounded",children:(0,t.jsxs)(n.Z,{defaultActiveKey:"1",centered:!0,children:[(0,t.jsxs)(n.<PERSON>.<PERSON>,{tab:"User List",children:[(0,t.jsx)("p",{className:"text-base mt-2",children:"On this page, the admin can view all user\u2019s accounts and information. The admin can update and delete any user using the action buttons."}),(0,t.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/user/list.png",alt:""})]},"1"),(0,t.jsxs)(n.Z.TabPane,{tab:"Wallet Deposits",children:[(0,t.jsx)("p",{className:"text-base mt-2",children:"On this page, the admin can view all users\u2019s wallet deposit history."}),(0,t.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/user/wallet-deposit.png",alt:""})]},"2"),(0,t.jsxs)(n.Z.TabPane,{tab:"Payment Records",children:[(0,t.jsx)("p",{className:"text-base mt-2",children:"On this page, the admin can view all users\u2019s payment history."}),(0,t.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/user/payment-records.png",alt:""})]},"3"),(0,t.jsxs)(n.Z.TabPane,{tab:"User Ratings",children:[(0,t.jsx)("p",{className:"text-base mt-2",children:"On this page, the admin can view all review and ratings thats are given by the user to a specific driver."}),(0,t.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/user/user-rating.png",alt:""})]},"4")]})})})}}},function(a){a.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,5236,3652,9774,2888,179],(function(){return e=7259,a(a.s=e);var e}));var e=a.O();_N_E=e}]);