(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3383],{5228:function(e,s,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/settings/push_notification",function(){return t(6816)}])},6816:function(e,s,t){"use strict";t.r(s);var i=t(5893);t(7294);s.default=function(){return(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:"Push notification credentials"}),(0,i.jsx)("div",{className:"m-4",children:(0,i.jsxs)("ul",{className:"list-disc",children:[(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Give the json file "}),"json file is collected from firebase console.",(0,i.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/pn/push.png",alt:""})]}),(0,i.jsx)("h5",{className:"border-b-[1px] border-black",children:"Getting the json file from firebase console"}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 1: Go to firebase"}),"Go to firebase console.Click add project.",(0,i.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/pn/gotofirebase.png",alt:""})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 2: Enter project name"}),"Enter project name and click continue.",(0,i.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/pn/name.png",alt:""})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 3: Chose platform"}),"Chose platform in which you want to send push-notification.",(0,i.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/pn/iosand.png",alt:""})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 4: Configuration"}),"configure the project.Fill the required details.After filling the details collect the settings file and upload it .",(0,i.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/pn/config.png",alt:""})]})]})})]})}}},function(e){e.O(0,[9774,2888,179],(function(){return s=5228,e(e.s=s);var s}));var s=e.O();_N_E=s}]);