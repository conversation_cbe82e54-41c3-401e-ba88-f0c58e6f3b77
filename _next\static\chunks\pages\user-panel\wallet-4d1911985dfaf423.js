(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3408],{4804:function(e,a,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/user-panel/wallet",function(){return s(9641)}])},9641:function(e,a,s){"use strict";s.r(a);var t=s(5893),l=(s(7294),s(3652)),n=s(5236);a.default=function(){return(0,t.jsx)(l.Z,{children:(0,t.jsx)("div",{className:"bg-yellow-50 bg-opacity-20 h-auto m-6 w-[90%] text-[16px] p-4 rounded ",children:(0,t.jsxs)(n.Z,{defaultActiveKey:"0",centered:!0,children:[(0,t.jsx)(n.Z.<PERSON>b<PERSON>ane,{tab:"Summary",children:(0,t.jsxs)("div",{className:"text-base",children:[(0,t.jsx)("p",{children:"Users can deposit money through various payment gateways. The summary page shows a glance at the user's wallet:"}),(0,t.jsxs)("ul",{className:"list-disc",children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("span",{className:"font-bold",children:"Total Deposits: "}),"The total amount of money that has been deposited by the user so far"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("span",{className:"font-bold",children:"Total Expense: "}),"The total amount of money that has been spent by the user so far"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("span",{className:"font-bold",children:"Available Balance: "}),"The money that is still available to spend"]})]}),(0,t.jsx)("p",{children:"A table is also here which is showing the latest deposit information. "}),(0,t.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/user-panel/wallet-summary.png",alt:"wallet-summary"}),(0,t.jsxs)("p",{className:"my-3",children:["By clicking the ",(0,t.jsx)("strong",{children:"Add Money"})," button, the user will be redirected to a page with a list of active payment gateways."]}),(0,t.jsxs)("ul",{className:"list-disc",children:[(0,t.jsx)("li",{children:"User will choose his/her preferred gateway."}),(0,t.jsx)("li",{children:"Input the amount in the input box"}),(0,t.jsx)("li",{children:"Submit and fill the credential form according to gateway"}),(0,t.jsx)("li",{children:"After successful deposit the user will be redirected to the summary home page."})]}),(0,t.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/user-panel/payment-gateways.png",alt:"payment-gateways"}),(0,t.jsx)("img",{className:"h-auto w-auto mx-auto mt-5",src:"/images/user-panel/payment.png",alt:"payment"})]})},0),(0,t.jsxs)(n.Z.TabPane,{tab:"Transactions",children:[(0,t.jsx)("p",{children:"The wallet transactions page shows the payment details only paid from the wallet."}),(0,t.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/user-panel/wallet-transactions.png",alt:"wallet-transactions"})]},1)]})})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,5236,3652,9774,2888,179],(function(){return a=4804,e(e.s=a);var a}));var a=e.O();_N_E=a}]);