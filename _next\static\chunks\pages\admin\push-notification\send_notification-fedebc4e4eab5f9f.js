(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9519],{8841:function(e,t,i){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/push-notification/send_notification",function(){return i(9505)}])},9505:function(e,t,i){"use strict";i.r(t);var s=i(5893);i(7294);t.default=function(){return(0,s.jsxs)("div",{children:[(0,s.jsxs)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:["Send Push Notifications to Applications"," "]}),(0,s.jsx)("div",{className:"m-4",children:(0,s.jsxs)("ul",{className:"list-disc",children:[(0,s.jsxs)("li",{className:"my-4",children:[(0,s.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 1 : Select the notification candidate."}),"System can send notifications to all users, drivers, or a specific group of users.To send a notification to a specific group of users, you need to select the group from the drop-down list.Or you can select all users or drivers.",(0,s.jsx)("img",{className:" w-auto mt-4",src:"/images/push_notifications/send_candidate.png",alt:""})]}),(0,s.jsxs)("li",{className:"my-4",children:[(0,s.jsxs)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:["Step 2 : Fill body"," "]}),"After selecting the notification candidate, you need to enter the title and body of the notification. The title and body of the notification will be displayed on the user's device."]}),(0,s.jsxs)("li",{className:"my-4",children:[(0,s.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 3 : Pick emoji"}),"select desired emoji with the help of emoji picker. It is optional.The emoji picker will be displayed when you click on the emoji icon.Selected emoji will be displayed in the body field.",(0,s.jsx)("img",{className:"h-auto w-auto my-4",src:"/images/push_notifications/emoji_in_send_page.png",alt:""})]}),(0,s.jsxs)("li",{className:"my-4",children:[(0,s.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 4 : Chose delivery time"}),"After entering the title and body of the notification, you need to select the delivery time.there are two options to select the delivery time.",(0,s.jsxs)("ul",{className:"list-disc",children:[(0,s.jsxs)("li",{className:"my-4",children:[(0,s.jsxs)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:["Step 4.1"," "]}),(0,s.jsx)("p",{children:"Send Now: If you select this option, the notification will be sent immediately."}),(0,s.jsx)("img",{className:"h-auto w-auto my-4",src:"/images/push_notifications/send_now.png",alt:""})]}),(0,s.jsxs)("li",{className:"my-4",children:[(0,s.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 4.2"}),(0,s.jsx)("p",{children:"Scheduled for Later: If you select this option, you need to select the date and time for the notification to be sent."}),(0,s.jsx)("img",{className:"h-auto w-auto my-4",src:"/images/push_notifications/send_scheduled_notification.png",alt:""})]})]})]}),(0,s.jsxs)("li",{className:"my-4",children:[(0,s.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 5"}),"After selecting the delivery time, you need to click on the send button to send the notification."]}),(0,s.jsxs)("li",{className:"my-4",children:[(0,s.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 6"}),(0,s.jsx)("p",{children:"Here is the example of the notification that will be sent to the user's device."}),(0,s.jsx)("img",{className:"h-auto w-auto my-4",src:"/images/push_notifications/phone_sample.png",alt:""})]})]})})]})}}},function(e){e.O(0,[9774,2888,179],(function(){return t=8841,e(e.s=t);var t}));var t=e.O();_N_E=t}]);