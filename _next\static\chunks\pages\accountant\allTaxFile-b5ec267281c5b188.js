(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4532],{5124:function(e,a,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/accountant/allTaxFile",function(){return s(2012)}])},2012:function(e,a,s){"use strict";s.r(a);var t=s(5893),n=(s(7294),s(3652));a.default=function(){return(0,t.jsx)(n.Z,{children:(0,t.jsxs)("div",{className:"bg-gray-50 h-auto m-6 w-[90%] text-[16px] p-4",children:[(0,t.jsxs)("div",{className:"my-4",children:[(0,t.jsx)("h5",{className:"border-b-[1px] border-black",children:"Accountant All Tax files"}),(0,t.jsxs)("div",{className:"m-4",children:[(0,t.jsx)("p",{children:"After assigning the accountant from the admin side, the corresponding accountant will get access to files for tax filing."}),(0,t.jsx)("img",{className:"w-auto h-auto my-2",src:"/images/acfile.png",alt:""}),(0,t.jsxs)("ul",{className:"list-disc",children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("span",{className:"font-medium text-red-500",children:"Download button and Action column: "}),"The accountant can download a single file. If he/she wants to download multiple files, he/she needs to mark the files and then download them. The files can be downloaded in Excel, PDF, or CSV format."]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("span",{className:"font-medium text-red-500",children:"Details button in action column: "}),"Can view the file information details specifically."]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("span",{className:"font-medium text-red-500",children:"Email: "})," The accountant can email users if necessary.",(0,t.jsx)("img",{className:"w-auto h-[300px] my-2",src:"/images/scemail.png",alt:""})]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("span",{className:"font-medium text-red-500",children:"Send File: "}),"After completing the work, the accountant will submit the files to the user using the send file option. When the accountant sends the file to the user, the administrator, user, and accountant will also receive a submission email.",(0,t.jsx)("img",{className:"w-auto h-auto border",src:"/images/sent.png",alt:""})]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("span",{className:"font-medium text-red-500",children:"Status:"}),"The accountant can change the file status or view the file review status.",(0,t.jsxs)("div",{className:"md:flex justify-around items-center flex-wrap gap-2",children:[(0,t.jsx)("img",{className:"w-auto h-auto border",src:"/images/status1.png",alt:""}),(0,t.jsx)("img",{className:"w-auto h-auto border",src:"/images/status2.png",alt:""})]}),(0,t.jsx)("p",{className:"shadow p-4 text-red-500 font-medium",children:"***If a file needs review, the accountant can see the status, but until the admin selects a specific accountant, the accountant cannot start working on that file.***"})]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("span",{className:"font-medium text-red-500",children:"Search: "}),"Accountant can search any file using ID"]}),(0,t.jsxs)("li",{children:["The accountant can manage the number of rows in the table throughout Show entries ",(0,t.jsx)("span",{className:"font-medium text-red-500",children:"Show entire"}),(0,t.jsx)("img",{className:"w-auto h-auto my-2",src:"/images/row.png",alt:""})]})]})]})]}),(0,t.jsxs)("div",{className:"my-4",children:[(0,t.jsx)("h5",{className:"border-b-[1px] border-black",children:"Details Of A Single Tax files"}),(0,t.jsxs)("div",{className:"m-4",children:[(0,t.jsx)("p",{children:"On this page, an accountant can see the details of a specific file."}),(0,t.jsx)("img",{className:"w-auto h-auto",src:"/images/acdetail.png",alt:""})]})]})]})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return a=5124,e(e.s=a);var a}));var a=e.O();_N_E=a}]);