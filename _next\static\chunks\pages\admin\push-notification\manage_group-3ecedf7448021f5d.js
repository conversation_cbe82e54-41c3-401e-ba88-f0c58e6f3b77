(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7760],{4923:function(e,s,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/push-notification/manage_group",function(){return a(7024)}])},7024:function(e,s,a){"use strict";a.r(s);var t=a(5893);a(7294);s.default=function(){return(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:"Manage push-notification Group"}),(0,t.jsx)("div",{className:"m-4",children:(0,t.jsx)("ul",{className:"list-disc",children:(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Descriptions"}),"you can manage your push-notification groups here. you can create, edit and delete groups.add users to groups and remove users from groups.also you can and multiple users to a group at once.",(0,t.jsxs)("ul",{className:"list-disc",children:[(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"step 1: Create Group"}),(0,t.jsx)("p",{children:" Click to the add new Notification Group button in the right up corner to create new push-notification group"})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"step 2: Manage Group"}),(0,t.jsx)("p",{children:" After creating the group , click the manage group button for each group , to manage the users of that group."}),(0,t.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/push_notifications/group.png"})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"step 3: Add users to the group"}),(0,t.jsx)("p",{children:" There are two way , you can add user to a group."}),(0,t.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/push_notifications/add_user_to_group.png"}),(0,t.jsxs)("ul",{className:"list-disc",children:[(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Add single users to the group"}),(0,t.jsx)("p",{children:"Click the add user button .This will popup a window containing all the available users available to be added to this group.to a single user , click add user, it wil add the user , to this group. This will remove the user from the available users list"}),(0,t.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/push_notifications/add_user_to_group2.png"})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Add Multiple users to the group"}),(0,t.jsx)("p",{children:"Select the users , by checking the checkbox. You can see, after selecting multiple user, a save button appear up in the modal.Click save to add users to the group"}),(0,t.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/push_notifications/multiple.png"})]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("p",{children:"Selected users will be added to the group"}),(0,t.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/push_notifications/confirmation.png"})]})]})]})]})]})})})]})}}},function(e){e.O(0,[9774,2888,179],(function(){return s=4923,e(e.s=s);var s}));var s=e.O();_N_E=s}]);