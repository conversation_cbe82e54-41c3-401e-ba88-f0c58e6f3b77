(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6131],{4916:function(e,s,i){(window.__NEXT_P=window.__NEXT_P||[]).push(["/driver-panel/trip-list",function(){return i(7617)}])},3401:function(e,s,i){"use strict";i.d(s,{O:function(){return t}});var n=i(5893),t=(i(7294),function(e){var s=e.heading;return(0,n.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] pb-2",children:s})})},7617:function(e,s,i){"use strict";i.r(s);var n=i(5893),t=(i(7294),i(3652)),a=i(3401),l=i(1664),r=i.n(l);s.default=function(){return(0,n.jsx)(t.Z,{children:(0,n.jsxs)("div",{className:"bg-yellow-50 bg-opacity-20 h-auto m-6 w-[90%] text-[16px] p-4 rounded",children:[(0,n.jsx)(a.O,{heading:"Trip List"}),(0,n.jsx)("div",{children:(0,n.jsxs)("div",{children:["The Driver can access various trip-related information in this section:",(0,n.jsxs)("ul",{className:"list-disc",children:[(0,n.jsxs)("li",{children:[(0,n.jsx)("span",{className:"font-bold",children:"User Name: "})," The name of the user who will be riding this trip."]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("span",{className:"font-bold",children:"Date: "}),"Date of the Trip"]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("span",{className:"font-bold",children:"Time: "}),"Time of the Trip"]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("span",{className:"font-bold",children:"Vehicle: "}),"Vehicle that is used for the trip"]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("span",{className:"font-bold",children:"Distance: "}),"Total distance of the trip"]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("span",{className:"font-bold",children:"Total Fare: "}),"The total cost of the trip."]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("span",{className:"font-bold",children:"Paid: "}),"The amount that has been paid by the user."]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("span",{className:"font-bold",children:"Due: "}),"Amount that still needed to be paid."]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("span",{className:"font-bold",children:"Earning: "}),"The amount that will be added to the drivers wallet for the trip."]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("span",{className:"font-bold",children:"Status: "}),"The Trip's present state. There are various statuses that can exist:",(0,n.jsxs)("ul",{className:"list-disc",children:[(0,n.jsxs)("li",{children:[(0,n.jsx)("span",{className:"text-yellow-500 font-semi-bold",children:"Pending: "})," The user has submitted a trip request, but no drivers have yet accepted it."]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("span",{className:"text-blue-500 font-semi-bold",children:"Accepted: "}),"A driver has agreed to take the trip."]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("span",{className:" font-semi-bold",children:"Moving: "}),"The Trip has begun, and the car is moving."]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("span",{className:"text-red-500 font-semi-bold",children:"Declined: "}),"The driver has denied to take the passengers."]})]})]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("span",{className:"font-bold",children:"Action: "}),"The user may examine additional information about the trip by clicking the eye icon in the Action column. The user will be sent to the \u201ctrip-details\u201d page after clicking the eye icon.More details can be found in ",(0,n.jsx)(r(),{href:"/trip-details",children:(0,n.jsx)("a",{target:"blank",className:"underline",children:"trip-details"})})," page."]})]})]})}),(0,n.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/driver-panel/trip-list.png",alt:""})]})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return s=4916,e(e.s=s);var s}));var s=e.O();_N_E=s}]);