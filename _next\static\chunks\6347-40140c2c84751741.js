"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6347],{7845:function(e,s,t){t.r(s);var i=t(5893);t(7294);s.default=function(){return(0,i.jsxs)("div",{children:[(0,i.jsxs)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:["All Push-notifications"," "]}),(0,i.jsx)("div",{className:"m-4",children:(0,i.jsx)("ul",{className:"list-disc",children:(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Descriptions"}),"All the push-notifications that are sent to the users will be displayed in this table.Push-notifications are sorted by the date and time they are sent. Table contains the following columns:",(0,i.jsxs)("ul",{className:"list-disc",children:[(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Title"}),(0,i.jsx)("p",{children:"It is the title of the notification that is sent to the user."})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Body"}),(0,i.jsx)("p",{children:"It is the body of the notification that is sent to the user."})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Status"}),(0,i.jsx)("p",{children:"a notification's status can be either success,scheduled or failed"})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Date"}),(0,i.jsx)("p",{children:"It is the date and time when the notification is sent to the user."})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Group Name"}),(0,i.jsxs)("p",{children:['It is the name of the group to which the notification is sent.if group name showed as "...", that means it is send to all user or driver or users.'," "]})]})]}),(0,i.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/push_notifications/all.png",alt:""})]})})})]})}},764:function(e,s,t){t.r(s);var i=t(5893);t(7294);s.default=function(){return(0,i.jsxs)("div",{children:[(0,i.jsxs)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:["Failed Push-notifications"," "]}),(0,i.jsx)("div",{className:"m-4",children:(0,i.jsx)("ul",{className:"list-disc",children:(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Descriptions"}),"All the push-notifications that are"," ",(0,i.jsx)("span",{className:"text-red-500",children:"failed to sent"})," will be displayed in this table.Push-notifications are sorted by the date and time they are sent. Table contains the following columns:",(0,i.jsxs)("ul",{className:"list-disc",children:[(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Title"}),(0,i.jsx)("p",{children:"It is the title of the notification that is sent to the user."})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Body"}),(0,i.jsx)("p",{children:"It is the body of the notification that is sent to the user."})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Status"}),(0,i.jsx)("p",{children:"a notification's status can be either success,scheduled or failed"})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Date"}),(0,i.jsx)("p",{children:"It is the date and time when the notification is sent to the user."})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Group Name"}),(0,i.jsxs)("p",{children:['It is the name of the group to which the notification is sent.if group name showed as "...", that means it is send to all user or driver or users.'," "]})]})]}),(0,i.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/push_notifications/failed.png",alt:""})]})})})]})}},6347:function(e,s,t){t.r(s);var i=t(5893),a=(t(7294),t(3652)),l=t(5236),o=t(7845),r=t(764),n=t(1049),c=t(9505),d=t(7024),h=[{title:"Send Notification",page:(0,i.jsx)(c.default,{})},{title:"All Notifications",page:(0,i.jsx)(o.default,{})},{title:"Failed Notifications",page:(0,i.jsx)(r.default,{})},{title:"Scheduled Notifications",page:(0,i.jsx)(n.default,{})},{title:"Manage Groups",page:(0,i.jsx)(d.default,{})}];s.default=function(){return(0,i.jsx)(a.Z,{children:(0,i.jsx)("div",{className:"bg-yellow-50 bg-opacity-20 h-auto m-6 w-[90%] text-[16px] p-4 rounded",children:(0,i.jsx)(l.Z,{defaultActiveKey:"1",centered:!0,children:h.map((function(e,s){return(0,i.jsx)(l.Z.TabPane,{tab:e.title,children:e.page},s+1)}))})})})}},7024:function(e,s,t){t.r(s);var i=t(5893);t(7294);s.default=function(){return(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:"Manage push-notification Group"}),(0,i.jsx)("div",{className:"m-4",children:(0,i.jsx)("ul",{className:"list-disc",children:(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Descriptions"}),"you can manage your push-notification groups here. you can create, edit and delete groups.add users to groups and remove users from groups.also you can and multiple users to a group at once.",(0,i.jsxs)("ul",{className:"list-disc",children:[(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"step 1: Create Group"}),(0,i.jsx)("p",{children:" Click to the add new Notification Group button in the right up corner to create new push-notification group"})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"step 2: Manage Group"}),(0,i.jsx)("p",{children:" After creating the group , click the manage group button for each group , to manage the users of that group."}),(0,i.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/push_notifications/group.png"})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"step 3: Add users to the group"}),(0,i.jsx)("p",{children:" There are two way , you can add user to a group."}),(0,i.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/push_notifications/add_user_to_group.png"}),(0,i.jsxs)("ul",{className:"list-disc",children:[(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Add single users to the group"}),(0,i.jsx)("p",{children:"Click the add user button .This will popup a window containing all the available users available to be added to this group.to a single user , click add user, it wil add the user , to this group. This will remove the user from the available users list"}),(0,i.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/push_notifications/add_user_to_group2.png"})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Add Multiple users to the group"}),(0,i.jsx)("p",{children:"Select the users , by checking the checkbox. You can see, after selecting multiple user, a save button appear up in the modal.Click save to add users to the group"}),(0,i.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/push_notifications/multiple.png"})]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("p",{children:"Selected users will be added to the group"}),(0,i.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/push_notifications/confirmation.png"})]})]})]})]})]})})})]})}},1049:function(e,s,t){t.r(s);var i=t(5893);t(7294);s.default=function(){return(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:"Scheduled Notifications"}),(0,i.jsx)("div",{className:"m-4",children:(0,i.jsxs)("ul",{className:"list-disc",children:["All the Scheduled push-notifications that are yet to sent to the users will be displayed in this table. Table contains the following columns:",(0,i.jsxs)("ul",{className:"list-disc",children:[(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Title"}),(0,i.jsx)("p",{children:"It is the title of the notification that is sent to the user."})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Body"}),(0,i.jsx)("p",{children:"It is the body of the notification that is sent to the user."})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Status"}),(0,i.jsx)("p",{children:"a notification's status can be either success,scheduled or failed"})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Scheduled Date"}),(0,i.jsx)("p",{children:"It is the date and time when the notification will be sent to the user."})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Group Name"}),(0,i.jsxs)("p",{children:['It is the name of the group to which the notification is sent.if group name showed as "...", that means it is send to all user or driver or users.'," "]})]})]}),(0,i.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/push_notifications/schedule.png",alt:""})]})})]})}},9505:function(e,s,t){t.r(s);var i=t(5893);t(7294);s.default=function(){return(0,i.jsxs)("div",{children:[(0,i.jsxs)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:["Send Push Notifications to Applications"," "]}),(0,i.jsx)("div",{className:"m-4",children:(0,i.jsxs)("ul",{className:"list-disc",children:[(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 1 : Select the notification candidate."}),"System can send notifications to all users, drivers, or a specific group of users.To send a notification to a specific group of users, you need to select the group from the drop-down list.Or you can select all users or drivers.",(0,i.jsx)("img",{className:" w-auto mt-4",src:"/images/push_notifications/send_candidate.png",alt:""})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsxs)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:["Step 2 : Fill body"," "]}),"After selecting the notification candidate, you need to enter the title and body of the notification. The title and body of the notification will be displayed on the user's device."]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 3 : Pick emoji"}),"select desired emoji with the help of emoji picker. It is optional.The emoji picker will be displayed when you click on the emoji icon.Selected emoji will be displayed in the body field.",(0,i.jsx)("img",{className:"h-auto w-auto my-4",src:"/images/push_notifications/emoji_in_send_page.png",alt:""})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 4 : Chose delivery time"}),"After entering the title and body of the notification, you need to select the delivery time.there are two options to select the delivery time.",(0,i.jsxs)("ul",{className:"list-disc",children:[(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsxs)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:["Step 4.1"," "]}),(0,i.jsx)("p",{children:"Send Now: If you select this option, the notification will be sent immediately."}),(0,i.jsx)("img",{className:"h-auto w-auto my-4",src:"/images/push_notifications/send_now.png",alt:""})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 4.2"}),(0,i.jsx)("p",{children:"Scheduled for Later: If you select this option, you need to select the date and time for the notification to be sent."}),(0,i.jsx)("img",{className:"h-auto w-auto my-4",src:"/images/push_notifications/send_scheduled_notification.png",alt:""})]})]})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 5"}),"After selecting the delivery time, you need to click on the send button to send the notification."]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 6"}),(0,i.jsx)("p",{children:"Here is the example of the notification that will be sent to the user's device."}),(0,i.jsx)("img",{className:"h-auto w-auto my-4",src:"/images/push_notifications/phone_sample.png",alt:""})]})]})})]})}}}]);