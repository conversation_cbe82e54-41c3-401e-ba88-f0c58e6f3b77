(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5263],{9139:function(e,t,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/driver-panel/withdraw",function(){return s(188)}])},3401:function(e,t,s){"use strict";s.d(t,{O:function(){return n}});var a=s(5893),n=(s(7294),function(e){var t=e.heading;return(0,a.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] pb-2",children:t})})},188:function(e,t,s){"use strict";s.r(t);var a=s(5893),n=(s(7294),s(3652)),r=s(3401);t.default=function(){return(0,a.jsx)(n.Z,{children:(0,a.jsxs)("div",{className:"bg-yellow-50 bg-opacity-20 h-auto m-6 w-[90%] text-[16px] p-4 rounded",children:[(0,a.jsx)(r.O,{heading:"Withdraw"}),(0,a.jsx)("p",{children:"Withdraw section is for making withdraw request. This page also shows a list of withdraws and their status"}),(0,a.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/driver-panel/withdraw.png",alt:""}),(0,a.jsx)("p",{className:"mt-3 font-semibold",children:"To make a new withdraw request a user have to follow the steps below:"}),(0,a.jsxs)("ul",{className:"list-disc",children:[(0,a.jsxs)("li",{children:[(0,a.jsx)("span",{className:"font-bold",children:"Step 1: "}),"Click on the withdraw button"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("span",{className:"font-bold",children:"Step 2: "}),"Fill out the withdraw request form"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("span",{className:"font-bold",children:"Step 3: "}),"Submit and wait for the admin to process the request"]})]}),(0,a.jsxs)("p",{children:["On submitting the request, the default status will be ",(0,a.jsx)("span",{className:"text-yellow-500",children:"Pending"}),"."]}),(0,a.jsxs)("p",{children:["After processing the request by the admin, the status will be changed to ",(0,a.jsx)("span",{className:"text-green-500",children:"Completed"}),"."]}),(0,a.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/driver-panel/withdraw-request.png",alt:""}),(0,a.jsx)("p",{className:"mt-3 font-bold",children:"While choosing the payment gateway, the user can add their own if the preferred gateway is not listed. The user must ensure correct detailed account information in the Account Details field"}),(0,a.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/driver-panel/withdraw-method.png",alt:""}),(0,a.jsx)("p",{className:"mt-3 font-bold text-white bg-[#FFA525] rounded px-4 py-2",children:"NOTE: If the user already has made a withdrawal request that has not been resolved by the admin yet, then the user can not make another withdrawal request"})]})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return t=9139,e(e.s=t);var t}));var t=e.O();_N_E=t}]);