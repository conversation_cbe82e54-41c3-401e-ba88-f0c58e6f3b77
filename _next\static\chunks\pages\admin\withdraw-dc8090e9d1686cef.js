(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[228],{9543:function(e,a,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/withdraw",function(){return t(9914)}])},9914:function(e,a,t){"use strict";t.r(a);var s=t(5893),i=(t(7294),t(3652));a.default=function(){return(0,s.jsx)("div",{children:(0,s.jsx)(i.Z,{children:(0,s.jsxs)("div",{className:"bg-yellow-50 bg-opacity-20 h-auto m-6 w-[90%] text-[16px] p-4 rounded",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] pb-2",children:"All Withdraw Request"}),(0,s.jsx)("p",{className:"text-base mt-2",children:"Here, the admin can view all withdraw requests created by drivers. The administrator can delete any withdrawal request using the action buttons on the right side."}),(0,s.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/admin/withdraw/withdraw-list.png",alt:""})]}),(0,s.jsxs)("div",{className:"my-6",children:[(0,s.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] pb-2",children:"Selected Method"}),(0,s.jsx)("p",{className:"text-base mt-2",children:"If you click on the selected method icon, you will see a drawer where you can see the withdrawal method and account details."}),(0,s.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/admin/withdraw/withdraw-method.png",alt:""})]}),(0,s.jsxs)("div",{className:"my-6",children:[(0,s.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] pb-2",children:"Manage Withdraw Request"}),(0,s.jsx)("p",{className:"text-base mt-2",children:"The admin can manage any withdrawal request by marking it as approved or disapproved. The admin can also manage status by changing it to completed, pending, cancelled, or processing."}),(0,s.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/admin/withdraw/withdraw-action.png",alt:""})]})]})})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return a=9543,e(e.s=a);var a}));var a=e.O();_N_E=a}]);