<!DOCTYPE html><html><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width"/><meta name="next-head-count" content="2"/><link rel="preload" href="/_next/static/css/ef96b46dffe113f8.css" as="style"/><link rel="stylesheet" href="/_next/static/css/ef96b46dffe113f8.css" data-n-g=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="/_next/static/chunks/polyfills-0d1b80a048d4787e.js"></script><script src="/_next/static/chunks/webpack-a146a8ef8f1e9d05.js" defer=""></script><script src="/_next/static/chunks/framework-dfb4b001ca8efcfd.js" defer=""></script><script src="/_next/static/chunks/main-9871903446ae15d0.js" defer=""></script><script src="/_next/static/chunks/pages/_app-e20749b88fa2b6ce.js" defer=""></script><script src="/_next/static/chunks/pages/admin/support-ticket/agents-450c3f14ee7097ff.js" defer=""></script><script src="/_next/static/KDIus1qkIHYmvN8aySE_u/_buildManifest.js" defer=""></script><script src="/_next/static/KDIus1qkIHYmvN8aySE_u/_ssgManifest.js" defer=""></script></head><body><div id="__next"><div><h4 class="text-center border-b-[1px] py-2 border-black">Support-Ticket Agents </h4><div class="m-4"><ul class="list-disc"><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Descriptions</p>Ticket-employee are created by the admin. Each ticket-employee  can reply to the ticket and can change the status of the ticket. Ticket will be automatically assigned to  ticket-employee  by  ticket department and category. This screen contains the list of all the agents that are created by the admin. Admin can create, edit, and delete agents.<img class="h-[500px] w-auto mt-4" src="/images/support_ticket/agent/agent.png" alt=""/></li><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Add Employee</p>To add a new Ticket employee, click on the <span class="font-semibold">Add Employee</span> button. Enter the name, email, password, and role in the respective fields. Click on the <span class="font-semibold">Save</span> button to save the employee.<p class="text-orange-600 font-semibold">             **               Note: Select Department and Designation for the employee must be equal to ticket &amp; support **</p><img class="w-auto mt-4" src="/images/support_ticket/agent/addagent.png" alt=""/></li></ul></div></div></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{}},"page":"/admin/support-ticket/agents","query":{},"buildId":"KDIus1qkIHYmvN8aySE_u","nextExport":true,"autoExport":true,"isFallback":false,"scriptLoader":[]}</script></body></html>