(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6697],{5390:function(e,a,i){(window.__NEXT_P=window.__NEXT_P||[]).push(["/live/privacyPolicy",function(){return i(2973)}])},2973:function(e,a,i){"use strict";i.r(a);var c=i(5893),s=(i(7294),i(3652));a.default=function(){return(0,c.jsx)(s.Z,{children:(0,c.jsxs)("div",{className:"bg-zinc-100 h-auto m-6 w-[90%] text-[16px] p-4",children:[(0,c.jsx)("h4",{className:"text-center font-semibold underline",children:"Privacy Policy Page"}),(0,c.jsxs)("div",{className:"my-4",children:[(0,c.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"Privacy Policy"}),(0,c.jsxs)("div",{className:"m-4",children:[(0,c.jsx)("p",{className:"text-base",children:"In this dynamic page, anyone can see a privacy policy page title, subtitle and some text about the privacy policy."}),(0,c.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/homepage/etc/privacy-policy.png",alt:""})]})]})]})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return a=5390,e(e.s=a);var a}));var a=e.O();_N_E=a}]);