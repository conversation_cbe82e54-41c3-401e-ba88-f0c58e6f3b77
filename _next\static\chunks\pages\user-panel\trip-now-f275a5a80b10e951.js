(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3745],{8340:function(e,s,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/user-panel/trip-now",function(){return a(3946)}])},3401:function(e,s,a){"use strict";a.d(s,{O:function(){return r}});var t=a(5893),r=(a(7294),function(e){var s=e.heading;return(0,t.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] pb-2",children:s})})},3946:function(e,s,a){"use strict";a.r(s);var t=a(5893),r=(a(7294),a(3652)),o=a(3401);a(1664);s.default=function(){return(0,t.jsx)(r.Z,{children:(0,t.jsxs)("div",{className:"bg-yellow-50 bg-opacity-20 h-auto m-6 w-[90%] text-[16px] p-4 rounded",children:[(0,t.jsx)(o.O,{heading:"Trip Now"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{children:"The user can take a ride from here:"}),(0,t.jsxs)("div",{className:"space-y-16",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h5",{className:"border-b w-1/2 pb-1 border-[#FFA525] text-[#FFA525]",children:"Step - 1"}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-bold",children:"Allow Location: "})," To take a ride you must have to allow your location."]}),(0,t.jsx)("img",{className:"h-auto w-auto mx-auto mt-2 shadow-sm",src:"/images/user-panel/allow-location.png",alt:""})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h5",{className:"border-b w-1/2 pb-1 border-[#FFA525] text-[#FFA525]",children:"Step - 2"}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-bold",children:"Select Service: "})," After allow location, you have to select a service to take a ride."]}),(0,t.jsx)("img",{className:"h-auto w-auto mx-auto mt-2 shadow-sm",src:"/images/user-panel/select-location.png",alt:""})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h5",{className:"border-b w-1/2 pb-1 border-[#FFA525] text-[#FFA525]",children:"Step - 3"}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-bold",children:"Select Destination: "})," Now you have to select your location from where you want to start ride and where is your destination."]}),(0,t.jsx)("img",{className:"h-auto w-auto mx-auto mt-2 shadow-sm",src:"/images/user-panel/destination-location.png",alt:""})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h5",{className:"border-b w-1/2 pb-1 border-[#FFA525] text-[#FFA525]",children:"Step - 4"}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-bold",children:"Select Transport: "})," Then you have to select a transport."]}),(0,t.jsx)("img",{className:"h-auto w-auto mx-auto mt-2 shadow-sm",src:"/images/user-panel/select-transport.png",alt:""})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h5",{className:"border-b w-1/2 pb-1 border-[#FFA525] text-[#FFA525]",children:"Step - 5"}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-bold",children:"Select Vehicle: "})," You have to select a vehicle after select transport."]}),(0,t.jsx)("img",{className:"h-auto w-auto mx-auto mt-2 shadow-sm",src:"/images/user-panel/select-car.png",alt:""})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h5",{className:"border-b w-1/2 pb-1 border-[#FFA525] text-[#FFA525]",children:"Step - 6"}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-bold",children:"Ride Now: "})," You will see this page after select a vehicle. Now click on the ride now button to take the ride."]}),(0,t.jsx)("img",{className:"h-auto w-auto mx-auto mt-2 shadow-sm",src:"/images/user-panel/ride-now.png",alt:""})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h5",{className:"border-b w-1/2 pb-1 border-[#FFA525] text-[#FFA525]",children:"Step - 7"}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-bold",children:"Confirmation Ride: "})," You can apply coupon here to get discount. Then you have to select a payment method and confirm the ride by clicking the confirm ride button."]}),(0,t.jsx)("img",{className:"h-auto w-auto mx-auto mt-2 shadow-sm",src:"/images/user-panel/confirm-ride.png",alt:""})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h5",{className:"border-b w-1/2 pb-1 border-[#FFA525] text-[#FFA525]",children:"Step - 8"}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-bold",children:"Request Ongoing: "})," By confirmation the ride you have to wait for the response of driver."]}),(0,t.jsx)("img",{className:"h-auto w-auto mx-auto mt-2 shadow-sm",src:"/images/user-panel/request.png",alt:""})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h5",{className:"border-b w-1/2 pb-1 border-[#FFA525] text-[#FFA525]",children:"Step - 9"}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-bold",children:"On Ride: "})," When driver will accept your ride, then you can direct call or message to the driver from here."]}),(0,t.jsx)("img",{className:"h-auto w-auto mx-auto mt-2 shadow-sm",src:"/images/user-panel/call-message.png",alt:""})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h5",{className:"border-b w-1/2 pb-1 border-[#FFA525] text-[#FFA525]",children:"Step - 10"}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-bold",children:"Payment: "})," After completing the ride, you have to payment the actual amount which is shown here. You can direct payment from your wallet (if your wallet balance is available) or you can payment by select a payment method which is provided in the list."]}),(0,t.jsx)("img",{className:"h-auto w-auto mx-auto mt-2 shadow-sm",src:"/images/user-panel/payment-method.png",alt:""})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h5",{className:"border-b w-1/2 pb-1 border-[#FFA525] text-[#FFA525]",children:"Step - 11"}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-bold",children:"Review & Rating: "})," ","Congratulations!! Your ride has been completed. Now you can give a review and rating to the driver."]}),(0,t.jsx)("img",{className:"h-auto w-auto mx-auto mt-2 shadow-sm",src:"/images/user-panel/review-rating.png",alt:""})]})]})]})]})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return s=8340,e(e.s=s);var s}));var s=e.O();_N_E=s}]);