(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4996],{7380:function(e,a,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/instruction/run",function(){return s(6386)}])},6386:function(e,a,s){"use strict";s.r(a);var i=s(5893),r=(s(1664),s(1163)),n=(s(7294),s(3652));a.default=function(){var e=(0,r.useRouter)();return(0,i.jsx)(n.Z,{children:(0,i.jsxs)("div",{className:"bg-gray-50 h-auto m-6 w-[90%] text-[16px] p-4",children:[(0,i.jsx)("h5",{className:"text-center font-bold capitalize",children:"Run time instructions"}),(0,i.jsx)("div",{className:"m-4 bg-green-500 p-4",children:(0,i.jsx)("p",{className:"text-center mb-0 text-white text-[20px]",children:"Make sure that, you have already filled up settings form and restart your backend server."})}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-black capitalize",children:"1. Admin login"}),(0,i.jsx)("p",{children:"To log in as admin, use the registered email when filling out the quick setup form."}),(0,i.jsx)("img",{className:"h-96 w-auto mx-auto",src:"/images/login.png",alt:""})]}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-black capitalize",children:"2. Website setting information fill up"}),(0,i.jsxs)("p",{children:["Open your admin dashboard, scroll to the sidebar and find website settings, then click on it, you need to fill out the information here, but for initial set up you need to fill out ",(0,i.jsx)("span",{className:"text-red-500",children:" Website Information, Manage SMTP Setting"})," and ",(0,i.jsx)("span",{className:"text-red-500",children:"Environment Setting"})," at first."]}),(0,i.jsx)("img",{className:"h-auto w-auto mx-auto",src:"/images/flow6.png",alt:""})]}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-black capitalize",children:"3. Set up common field data"}),(0,i.jsxs)("p",{children:["To set up common field data ",(0,i.jsx)("span",{className:"cursor-pointer underline text-red-500",onClick:function(){e.push("/admin/userForms/commonField/")},children:"Click here"}),"."]})]}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-black capitalize",children:"4. Add user role "}),(0,i.jsx)("p",{children:"Now you need to add your user role. "}),(0,i.jsx)("img",{className:"h-auto w-auto mx-auto",src:"/images/roleadd.png",alt:""})]}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-black capitalize",children:"5. Add tax price value "}),(0,i.jsx)("p",{children:"Now you need to set up the tax price value information. "}),(0,i.jsx)("img",{className:"h-auto w-auto mx-auto",src:"/images/tax.png",alt:""})]}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-black capitalize",children:"6. Add province information "}),(0,i.jsx)("p",{children:"Now you need to set up your province information. "}),(0,i.jsx)("img",{className:"h-auto w-auto mx-auto",src:"/images/provicen.png",alt:""})]}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-black capitalize",children:"7. Add coupon information "}),(0,i.jsx)("p",{children:"Now you need to set up your coupon information. "}),(0,i.jsx)("img",{className:"h-auto w-auto mx-auto",src:"/images/coupon.png",alt:""})]}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-black capitalize",children:"8. Set up live page information  "}),(0,i.jsx)("p",{children:"Now you need to fill out this all information to run your live page."}),(0,i.jsx)("img",{className:"h-[500px] w-auto mx-auto",src:"/images/flow7.png",alt:""})]}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-black capitalize",children:"9. Accountant and New user add "}),(0,i.jsx)("p",{children:"And lastly, you can add accountants and users as per your wish using signup."}),(0,i.jsx)("img",{className:"h-[500px] w-auto mx-auto",src:"/images/signup.png",alt:""})]}),(0,i.jsxs)("p",{className:"p-4 shadow text-center text-red-500 font-bold",children:["If you complete the entire procedure correctly, your website will be up and running. ",(0,i.jsx)("br",{}),"If you face any problem we will provide free Support."]})]})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return a=7380,e(e.s=a);var a}));var a=e.O();_N_E=a}]);