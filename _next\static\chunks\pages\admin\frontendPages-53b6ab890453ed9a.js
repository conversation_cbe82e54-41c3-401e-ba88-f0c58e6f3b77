(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2380],{28:function(e,a,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/frontendPages",function(){return s(5671)}])},5671:function(e,a,s){"use strict";s.r(a);var n=s(5893),t=(s(7294),s(3652)),i=s(5236);a.default=function(){return(0,n.jsx)(t.Z,{children:(0,n.jsx)("div",{className:"bg-yellow-50 bg-opacity-20 h-auto m-6 w-[90%] text-[16px] p-4 rounded",children:(0,n.jsxs)(i.Z,{defaultActiveKey:"1",centered:!0,children:[(0,n.jsxs)(i.Z.<PERSON>b<PERSON>ane,{tab:"Landing Page",children:[(0,n.jsx)("h4",{className:"text-center font-semibold underline",children:"Landing Page"}),(0,n.jsxs)("p",{className:"text-base text-center",children:["First, you need to go to the admin panel > frontend pages > landing page, where you will see five sections. Here, you have to fill out all the forms in different languages, and these details will be shown on the landing page."," "]}),(0,n.jsxs)("div",{className:"my-4",children:[(0,n.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"Hero Section"}),(0,n.jsxs)("div",{className:"m-4",children:[(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize",children:"admin panel"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/frontend-pages/landing-page/hero.png",alt:""}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"landing page"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/frontend-pages/landing-page/hero-land.png",alt:""})]})]}),(0,n.jsxs)("div",{className:"my-4",children:[(0,n.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"Brand Partner Section"}),(0,n.jsxs)("div",{className:"m-4",children:[(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize",children:"admin panel"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/frontend-pages/landing-page/brand.png",alt:""}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"landing page"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/frontend-pages/landing-page/brand-land.png",alt:""})]})]}),(0,n.jsxs)("div",{className:"my-4",children:[(0,n.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"Work Section"}),(0,n.jsxs)("div",{className:"m-4",children:[(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize",children:"admin panel"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/frontend-pages/landing-page/work.png",alt:""}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"landing page"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/frontend-pages/landing-page/work-land.png",alt:""})]})]}),(0,n.jsxs)("div",{className:"my-4",children:[(0,n.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"Numeric Statistics Section"}),(0,n.jsxs)("div",{className:"m-4",children:[(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize",children:"admin panel"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/frontend-pages/landing-page/numeric.png",alt:""}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"landing page"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/frontend-pages/landing-page/numeric-land.png",alt:""})]})]}),(0,n.jsxs)("div",{className:"my-4",children:[(0,n.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"Benifit Section"}),(0,n.jsxs)("div",{className:"m-4",children:[(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize",children:"admin panel"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm mx-auto",src:"/images/admin/frontend-pages/landing-page/benifit.png",alt:""}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"landing page"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/frontend-pages/landing-page/benifit-land.png",alt:""})]})]})]},"1"),(0,n.jsxs)(i.Z.TabPane,{tab:"Login",children:[(0,n.jsx)("h4",{className:"text-center font-semibold underline",children:"Login Page"}),(0,n.jsxs)("p",{className:"text-base",children:["First, you need to go to the admin panel > frontend pages > login page, where you will see a section. Here, you have to input images in different languages, and the image will be shown on the login page."," "]}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"admin panel"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/frontend-pages/landing-page/login.png",alt:""}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"login page"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/frontend-pages/landing-page/login-land.png",alt:""})]},"2"),(0,n.jsxs)(i.Z.TabPane,{tab:"Contact",children:[(0,n.jsx)("h4",{className:"text-center font-semibold underline",children:"Contact Page"}),(0,n.jsxs)("p",{className:"text-base",children:["First, you need to go to the admin panel > frontend pages > contact page, where you will see 3 sections. Here, you have to fill out all the forms in different languages, and the information will be shown on the contact page."," "]}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"admin panel"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/frontend-pages/landing-page/contact.png",alt:""}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/frontend-pages/landing-page/contact-information.png",alt:""}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"contact page"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/frontend-pages/landing-page/contact-land.png",alt:""}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm mt-4",src:"/images/admin/frontend-pages/landing-page/contact-information-land.png",alt:""})]},"3"),(0,n.jsxs)(i.Z.TabPane,{tab:"Faq",children:[(0,n.jsx)("h4",{className:"text-center font-semibold underline",children:"Faq Page"}),(0,n.jsxs)("p",{className:"text-base",children:["First, you need to go to the admin panel > frontend pages > faq page, where you will see a form. Here, you can delete, update, and create a new FAQ in different languages, and the FAQs will be shown on the FAQ page."," "]}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"admin panel"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/frontend-pages/landing-page/faq.png",alt:""}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"faq page"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/frontend-pages/landing-page/faq-land.png",alt:""})]},"4"),(0,n.jsxs)(i.Z.TabPane,{tab:"About",children:[(0,n.jsx)("h4",{className:"text-center font-semibold underline",children:"About Page"}),(0,n.jsxs)("p",{className:"text-base",children:["First, you need to go to the admin panel > frontend pages > about page, where you will see a form. Here, you can create an about page by filling out all the forms in different languages, and the contents will be shown on the about page."," "]}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"admin panel"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/frontend-pages/landing-page/about.png",alt:""}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"about page"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm mx-auto",src:"/images/admin/frontend-pages/landing-page/about-land.png",alt:""})]},"5"),(0,n.jsxs)(i.Z.TabPane,{tab:"Terms & Conditions",children:[(0,n.jsx)("h4",{className:"text-center font-semibold underline",children:"Terms & Conditions Page"}),(0,n.jsxs)("p",{className:"text-base",children:["First, you need to go to the admin panel > frontend pages > terms & conditions page, where you will see a form. Here, you can create a terms & conditions page by filling out all the forms in different languages, and the contents will be shown on the terms & conditions page."," "]}),(0,n.jsx)("p",{className:"text-base py-4 text-white rounded-md bg-[#FFA525] font-semibold mt-5 text-center",children:"Creating a terms & conditions page is the same process as creating an about page."})]},"6"),(0,n.jsxs)(i.Z.TabPane,{tab:"Privacy Policy",children:[(0,n.jsx)("h4",{className:"text-center font-semibold underline",children:"Privacy Policy Page"}),(0,n.jsxs)("p",{className:"text-base",children:["First, you need to go to the admin panel > frontend pages > privacy policy page, where you will see a form. Here, you can create a privacy policy page by filling out all the forms in different languages, and the contents will be shown on the privacy policy page."," "]}),(0,n.jsx)("p",{className:"text-base py-4 text-white rounded-md bg-[#FFA525] font-semibold mt-5 text-center",children:"Creating a privacy policy page is the same process as creating an about page."})]},"7"),(0,n.jsxs)(i.Z.TabPane,{tab:"Service",children:[(0,n.jsx)("h4",{className:"text-center font-semibold underline",children:"Service Page"}),(0,n.jsxs)("p",{className:"text-base",children:["First, you need to go to the admin panel > frontend pages > service page, where you will see a list of services. Here, you have to fill out all the forms for each service in different languages, and these data will be shown on the landing page."," "]}),(0,n.jsxs)("div",{className:"my-4",children:[(0,n.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"Hero Section"}),(0,n.jsxs)("div",{className:"m-4",children:[(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize",children:"admin panel"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/frontend-pages/service/service.png",alt:""}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"landing page header section"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/frontend-pages/service/service-header-land.png",alt:""}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"service page > car"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/frontend-pages/service/service-land.png",alt:""})]})]}),(0,n.jsxs)("div",{className:"my-4",children:[(0,n.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"2nd Section"}),(0,n.jsxs)("div",{className:"m-4",children:[(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize",children:"admin panel"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm mx-auto",src:"/images/admin/frontend-pages/service/second.png",alt:""}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"service page"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/frontend-pages/service/second-land.png",alt:""})]})]}),(0,n.jsxs)("div",{className:"my-4",children:[(0,n.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"Last Section"}),(0,n.jsxs)("div",{className:"m-4",children:[(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize",children:"admin panel"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm mx-auto",src:"/images/admin/frontend-pages/service/last.png",alt:""}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"service page"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm mx-auto",src:"/images/admin/frontend-pages/service/last-land.png",alt:""})]})]})]},"8"),(0,n.jsxs)(i.Z.TabPane,{tab:"Business",children:[(0,n.jsx)("h4",{className:"text-center font-semibold underline",children:"Business Page"}),(0,n.jsxs)("p",{className:"text-base",children:["First, you need to go to the admin panel > frontend pages > business page, where you will see a form. Here, you can create a business page by filling out all the forms in different languages, and the contents will be shown on the business page."," "]}),(0,n.jsx)("p",{className:"text-base py-4 text-white rounded-md bg-[#FFA525] font-semibold mt-5 text-center",children:"Creating a business page is the same process as creating an about page."})]},"9"),(0,n.jsxs)(i.Z.TabPane,{tab:"Safety",children:[(0,n.jsx)("h4",{className:"text-center font-semibold underline",children:"Safety Page"}),(0,n.jsxs)("p",{className:"text-base",children:["First, you need to go to the admin panel > frontend pages > safety page, where you will see a form. Here, you can create a safety page by filling out all the forms in different languages, and the contents will be shown on the safety page."," "]}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"admin panel"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/frontend-pages/landing-page/safety.png",alt:""}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"safety page"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm mx-auto",src:"/images/admin/frontend-pages/landing-page/safety-land.png",alt:""})]},"10"),(0,n.jsxs)(i.Z.TabPane,{tab:"Blog",children:[(0,n.jsx)("h4",{className:"text-center font-semibold underline",children:"Blog Page"}),(0,n.jsxs)("p",{className:"text-base",children:["First, you need to go to the admin panel > frontend pages > blog page, where you will see a form. Here, you can delete, update, and create a new blog in different languages, and the blogs will be shown on the blog page."," "]}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"admin panel"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/frontend-pages/landing-page/blog.png",alt:""}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"blog page"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm mx-auto",src:"/images/admin/frontend-pages/landing-page/blog-land.png",alt:""})]},"11"),(0,n.jsxs)(i.Z.TabPane,{tab:"Press",children:[(0,n.jsx)("h4",{className:"text-center font-semibold underline",children:"Press Page"}),(0,n.jsxs)("p",{className:"text-base",children:["First, you need to go to the admin panel > frontend pages > press, where you will see a form. Here, you can delete, update, and create a new press in different languages, and the presses will be shown on the press page."," "]}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"admin panel"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/frontend-pages/landing-page/press.png",alt:""}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"press page"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm mx-auto",src:"/images/admin/frontend-pages/landing-page/press-land.png",alt:""})]},"12"),(0,n.jsxs)(i.Z.TabPane,{tab:"Help & Support",children:[(0,n.jsx)("p",{className:"text-base",children:"First, you need to go to the admin panel > frontend pages > help & support page. On this page, you can add title, subtitle and description. These information will be shown in the mobile apps."}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"admin panel"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/frontend-pages/landing-page/help-support.png",alt:""})]},"13"),(0,n.jsxs)(i.Z.TabPane,{tab:"Custom",children:[(0,n.jsx)("h4",{className:"text-center font-semibold underline",children:"Custom Page"}),(0,n.jsxs)("p",{className:"text-base",children:["First, you need to go to the admin panel > frontend pages > custom page. On this page, you can add an additional page for your website, where you can specify the menu or footer to display. You can delete or update all custom page data. Click on the"," ",(0,n.jsx)("span",{className:"text-[#FFA525] font-semibold",children:"Add New Page"})," ","button and submit the title of your custom page. Then you have to fill out all the input forms for a specific custom page in different languages."]}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"admin panel"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/frontend-pages/custom-page/custom.png",alt:""}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"landing page header section"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm mx-auto",src:"/images/admin/frontend-pages/custom-page/custom-heading-land.png",alt:""}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"landing page footer section"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm mx-auto",src:"/images/admin/frontend-pages/custom-page/custom-footer-land.png",alt:""}),(0,n.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"custom page"}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm mx-auto",src:"/images/admin/frontend-pages/custom-page/custom-land.png",alt:""})]},"14")]})})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,5236,3652,9774,2888,179],(function(){return a=28,e(e.s=a);var a}));var a=e.O();_N_E=a}]);