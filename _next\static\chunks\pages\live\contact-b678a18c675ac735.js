(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4628],{4:function(n,a,e){(window.__NEXT_P=window.__NEXT_P||[]).push(["/live/contact",function(){return e(9030)}])},9030:function(n,a,e){"use strict";e.r(a);var s=e(5893),c=(e(7294),e(3652));a.default=function(){return(0,s.jsx)(c.Z,{children:(0,s.jsxs)("div",{className:"bg-zinc-100 h-auto m-6 w-[90%] text-[16px] p-4",children:[(0,s.jsx)("h4",{className:"text-center font-semibold underline",children:"Contact Page"}),(0,s.jsxs)("div",{className:"my-4",children:[(0,s.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"contact"}),(0,s.jsxs)("div",{className:"m-4",children:[(0,s.jsx)("p",{className:"text-base",children:"In this section, anyone can see a contact page form along with company contact details. This section is also dynamic."}),(0,s.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/homepage/contact/contact.png",alt:""})]})]})]})})}}},function(n){n.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return a=4,n(n.s=a);var a}));var a=n.O();_N_E=a}]);