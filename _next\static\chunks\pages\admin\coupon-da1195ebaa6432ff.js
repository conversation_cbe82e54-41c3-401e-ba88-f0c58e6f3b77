(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7865],{4720:function(a,e,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/coupon",function(){return n(3299)}])},3299:function(a,e,n){"use strict";n.r(e);var s=n(5893),o=(n(7294),n(3652));e.default=function(){return(0,s.jsx)(o.Z,{children:(0,s.jsxs)("div",{className:"bg-yellow-50 bg-opacity-20 h-auto m-6 w-[90%] text-[16px] p-4 rounded",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] pb-2",children:"Coupon Create"}),(0,s.jsx)("p",{className:"text-base mt-2",children:"You can create any kind of coupon by filling out the forms. You also can assign coupon available duration."}),(0,s.jsx)("img",{className:"h-auto w-auto mx-auto mt-2 shadow-sm",src:"/images/admin/coupon/create.png",alt:""})]}),(0,s.jsxs)("div",{className:"my-6",children:[(0,s.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] pb-2",children:"Coupon List"}),(0,s.jsx)("p",{className:"text-base mt-2",children:"Here, the admin can view all coupon list and also can active or inactive, edit and delete."}),(0,s.jsx)("img",{className:"h-auto w-auto mx-auto mt-2 shadow-sm",src:"/images/admin/coupon/list.png",alt:""})]})]})})}}},function(a){a.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return e=4720,a(a.s=e);var e}));var e=a.O();_N_E=e}]);