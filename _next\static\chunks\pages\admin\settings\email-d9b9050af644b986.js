(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[150],{7167:function(e,s,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/settings/email",function(){return a(4692)}])},4692:function(e,s,a){"use strict";a.r(s);var t=a(5893);a(7294);s.default=function(){return(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:"Email settings"}),(0,t.jsxs)("div",{className:"m-4",children:[(0,t.jsx)("p",{className:"text-[18px] font-semibold",children:"To send email from the system,email credential need to be full-fill . There are three email option to chose from"}),(0,t.jsxs)("p",{className:"text-[18px] font-bold text-green-600",children:["** Select the default email. Email always send from default email **"," "]}),(0,t.jsxs)("ul",{className:"list-disc",children:[(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"SendGrid Smtp"}),"sendgrid is a cloud-based email service that provides reliable transactional and marketing email delivery. It is a great option for sending emails from your application. It is a great option for sending emails from your application. Go to"," ",(0,t.jsx)("a",{href:"https://sendgrid.com/",target:"_blank",rel:"noreferrer",className:"text-blue-500",children:"https://sendgrid.com/"})," ","and create an account.",(0,t.jsx)("p",{className:"text-[18px] font-semibold text-green-500",children:"After creating an account, you will get an API key. Copy the API key and paste it in the API key field."}),(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/email/sendgrid.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Gmail Provider"}),"Gmail is a free email service provided by Google. It is a great option for sending emails from your application.",(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/email/gmail.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Other Provider"}),"other provider can be used to send email from the system.",(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/email/other.png",alt:""})]})]})]})]})}}},function(e){e.O(0,[9774,2888,179],(function(){return s=7167,e(e.s=s);var s}));var s=e.O();_N_E=s}]);