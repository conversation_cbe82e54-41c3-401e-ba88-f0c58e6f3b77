(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4606],{6097:function(e,s,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/instruction/frontend",function(){return t(9638)}])},9638:function(e,s,t){"use strict";t.r(s);var a=t(5893),r=t(1664),n=t.n(r);t(7294),t(3652);s.default=function(){return(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:"Frontend Installation Process"}),(0,a.jsx)("div",{className:"m-4",children:(0,a.jsxs)("ul",{className:"list-disc",children:[(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 1"}),"Open the folder first.",(0,a.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/front.png",alt:""})]}),(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 2"}),"Then you need to find the ",(0,a.jsx)("span",{className:" text-red-400",children:" next.config.js"})," file and open it in your appropriate text editor.",(0,a.jsx)("img",{className:"h-[500px] w-auto my-4",src:"/images/f2.png",alt:""}),(0,a.jsxs)("p",{children:["Because you need to change the necessary information of your website such as ",(0,a.jsx)("span",{className:" text-red-400",children:"BACKEND URL, the text is underlined in the image below"}),", in this case, you must be provided your personal backend domain or sub domain URL and save it."]}),(0,a.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/f3.png",alt:""})]}),(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 3"}),"Then you need to open your terminal or command prompt (mac and linus user) or windows user you have to follow the picture. Select the address bar and type ",(0,a.jsx)("span",{className:" text-red-400",children:"cmd"}),(0,a.jsx)("img",{className:"h-[500px] w-auto my-4",src:"/images/f5.png",alt:""}),(0,a.jsxs)("p",{children:["Then the command prompt open. Type ",(0,a.jsx)("span",{className:" text-red-400",children:"yarn run buildexport"})," and hit enter"]}),(0,a.jsx)("img",{className:"h-auto w-auto my-4",src:"/images/f6.png",alt:""}),(0,a.jsxs)("p",{children:["After build successfully, you can see ",(0,a.jsx)("span",{className:" text-red-400",children:"Export successful"})," message and then you will get ",(0,a.jsx)("span",{className:" text-red-400",children:"out"})," folder"]}),(0,a.jsx)("img",{className:"h-auto w-auto my-4",src:"/images/f7.png",alt:""})]}),(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 4"}),(0,a.jsxs)("p",{children:["Now zip the ",(0,a.jsx)("span",{className:" text-red-400",children:"out"})," folder and upload it to your server."]}),(0,a.jsx)("img",{className:"h-auto w-auto my-4",src:"/images/f8.png",alt:""})]}),(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 5"}),(0,a.jsxs)("p",{children:["Then you go to your server panel or cPanel, Select your ",(0,a.jsx)("span",{className:" text-red-400",children:"Document Root"})," and right click on your mouse."]}),(0,a.jsx)("img",{className:"h-auto w-auto my-4",src:"/images/f9.png",alt:""}),(0,a.jsx)("p",{className:"text-[20px] text-red-500 font-bold",children:"** Now you need to follow the backend installation process Step 2 - Step 10"}),(0,a.jsx)("p",{className:"text-[20px] text-red-500 font-bold",children:"Note: in Step 7 of backend installation process, in the domain name section please confirm that it will be the root domain (Not a subdomain). For example, your_main_domain.com"})]}),(0,a.jsxs)("p",{className:"border-[1px] border-green-500 text-center p-2 text-green-500 font-semibold text-[24px]",children:["After successful completion of all the processes, your website will go live, visit your domain url, for example ",(0,a.jsx)("span",{className:"italic ",children:"your_main_domain.com"})," "]}),(0,a.jsx)("p",{className:"text-right",children:(0,a.jsx)(n(),{href:"mailto:<EMAIL>",children:(0,a.jsx)("a",{className:"lg:mr-6 hover:cursor-pointer font-[Montserrat]",children:"***** <EMAIL> *****"})})})]})})]})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return s=6097,e(e.s=s);var s}));var s=e.O();_N_E=s}]);