(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9018],{4099:function(e,a,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/live/blog",function(){return n(7179)}])},7179:function(e,a,n){"use strict";n.r(a);var s=n(5893),t=(n(7294),n(3652));a.default=function(){return(0,s.jsx)(t.Z,{children:(0,s.jsxs)("div",{className:"bg-zinc-100 h-auto m-6 w-[90%] text-[16px] p-4",children:[(0,s.jsx)("h4",{className:"text-center font-semibold underline",children:"Blog Page"}),(0,s.jsxs)("div",{className:"my-4",children:[(0,s.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"blog"}),(0,s.jsxs)("div",{className:"m-4",children:[(0,s.jsx)("p",{className:"text-base",children:"Here, anyone can see a blog page along with a title, subtitle, and some blog. The title, subtitle, and every blog are dynamic and can be created, updated, or deleted in the admin panel."}),(0,s.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/homepage/blog/blog.png",alt:""})]})]})]})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return a=4099,e(e.s=a);var a}));var a=e.O();_N_E=a}]);