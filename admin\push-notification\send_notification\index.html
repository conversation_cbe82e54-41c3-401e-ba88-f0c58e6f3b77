<!DOCTYPE html><html><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width"/><meta name="next-head-count" content="2"/><link rel="preload" href="/_next/static/css/ef96b46dffe113f8.css" as="style"/><link rel="stylesheet" href="/_next/static/css/ef96b46dffe113f8.css" data-n-g=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="/_next/static/chunks/polyfills-0d1b80a048d4787e.js"></script><script src="/_next/static/chunks/webpack-a146a8ef8f1e9d05.js" defer=""></script><script src="/_next/static/chunks/framework-dfb4b001ca8efcfd.js" defer=""></script><script src="/_next/static/chunks/main-9871903446ae15d0.js" defer=""></script><script src="/_next/static/chunks/pages/_app-e20749b88fa2b6ce.js" defer=""></script><script src="/_next/static/chunks/pages/admin/push-notification/send_notification-fedebc4e4eab5f9f.js" defer=""></script><script src="/_next/static/KDIus1qkIHYmvN8aySE_u/_buildManifest.js" defer=""></script><script src="/_next/static/KDIus1qkIHYmvN8aySE_u/_ssgManifest.js" defer=""></script></head><body><div id="__next"><div><h4 class="text-center border-b-[1px] py-2 border-black">Send Push Notifications to Applications<!-- --> </h4><div class="m-4"><ul class="list-disc"><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Step 1 : Select the notification candidate.</p>System can send notifications to all users, drivers, or a specific group of users.To send a notification to a specific group of users, you need to select the group from the drop-down list.Or you can select all users or drivers.<img class=" w-auto mt-4" src="/images/push_notifications/send_candidate.png" alt=""/></li><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Step 2 : Fill body<!-- --> </p>After selecting the notification candidate, you need to enter the title and body of the notification. The title and body of the notification will be displayed on the user&#x27;s device.</li><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Step 3 : Pick emoji</p>select desired emoji with the help of emoji picker. It is optional.The emoji picker will be displayed when you click on the emoji icon.Selected emoji will be displayed in the body field.<img class="h-auto w-auto my-4" src="/images/push_notifications/emoji_in_send_page.png" alt=""/></li><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Step 4 : Chose delivery time</p>After entering the title and body of the notification, you need to select the delivery time.there are two options to select the delivery time.<ul class="list-disc"><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Step 4.1<!-- --> </p><p>Send Now: If you select this option, the notification will be sent immediately.</p><img class="h-auto w-auto my-4" src="/images/push_notifications/send_now.png" alt=""/></li><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Step 4.2</p><p>Scheduled for Later: If you select this option, you need to select the date and time for the notification to be sent.</p><img class="h-auto w-auto my-4" src="/images/push_notifications/send_scheduled_notification.png" alt=""/></li></ul></li><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Step 5</p>After selecting the delivery time, you need to click on the send button to send the notification.</li><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Step 6</p><p>Here is the example of the notification that will be sent to the user&#x27;s device.</p><img class="h-auto w-auto my-4" src="/images/push_notifications/phone_sample.png" alt=""/></li></ul></div></div></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{}},"page":"/admin/push-notification/send_notification","query":{},"buildId":"KDIus1qkIHYmvN8aySE_u","nextExport":true,"autoExport":true,"isFallback":false,"scriptLoader":[]}</script></body></html>