(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6499],{9179:function(e,s,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/live/about",function(){return a(8810)}])},8810:function(e,s,a){"use strict";a.r(s);var n=a(5893),t=(a(7294),a(3652));s.default=function(){return(0,n.jsx)(t.Z,{children:(0,n.jsxs)("div",{className:"bg-zinc-100 h-auto m-6 w-[90%] text-[16px] p-4",children:[(0,n.jsx)("h4",{className:"text-center font-semibold underline",children:"About Page"}),(0,n.jsxs)("div",{className:"my-4",children:[(0,n.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"About"}),(0,n.jsxs)("div",{className:"m-4",children:[(0,n.jsx)("p",{className:"text-base",children:"In this dynamic page, anyone can see a about page title, subtitle and some content."}),(0,n.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/homepage/etc/about.png",alt:""})]})]})]})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return s=9179,e(e.s=s);var s}));var s=e.O();_N_E=s}]);