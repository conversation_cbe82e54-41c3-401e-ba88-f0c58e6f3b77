(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6422],{3543:function(e,t,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/support-ticket/agents",function(){return a(4377)}])},4377:function(e,t,a){"use strict";a.r(t);var s=a(5893);a(7294);t.default=function(){return(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:"Support-Ticket Agents "}),(0,s.jsx)("div",{className:"m-4",children:(0,s.jsxs)("ul",{className:"list-disc",children:[(0,s.jsxs)("li",{className:"my-4",children:[(0,s.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Descriptions"}),"Ticket-employee are created by the admin. Each ticket-employee  can reply to the ticket and can change the status of the ticket. Ticket will be automatically assigned to  ticket-employee  by  ticket department and category. This screen contains the list of all the agents that are created by the admin. Admin can create, edit, and delete agents.",(0,s.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/support_ticket/agent/agent.png",alt:""})]}),(0,s.jsxs)("li",{className:"my-4",children:[(0,s.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Add Employee"}),"To add a new Ticket employee, click on the ",(0,s.jsx)("span",{className:"font-semibold",children:"Add Employee"})," button. Enter the name, email, password, and role in the respective fields. Click on the ",(0,s.jsx)("span",{className:"font-semibold",children:"Save"})," button to save the employee.",(0,s.jsx)("p",{className:"text-orange-600 font-semibold",children:"             **               Note: Select Department and Designation for the employee must be equal to ticket & support **"}),(0,s.jsx)("img",{className:"w-auto mt-4",src:"/images/support_ticket/agent/addagent.png",alt:""})]})]})})]})}}},function(e){e.O(0,[9774,2888,179],(function(){return t=3543,e(e.s=t);var t}));var t=e.O();_N_E=t}]);