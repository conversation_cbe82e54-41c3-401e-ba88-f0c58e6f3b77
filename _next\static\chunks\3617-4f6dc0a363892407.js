(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3617],{2020:function(e,t,n){"use strict";n.d(t,{Z:function(){return a}});var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)};Object.create;Object.create;var o=n(7294),i=n(5098),s=n.n(i),l=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}(t,e),t.prototype.componentDidMount=function(){this.mm=new(s())(this.el,this.props)},t.prototype.componentWillUnmount=function(){this.mm.dispose()},t.prototype.render=function(){var e=this,t=this.props,n=t.subMenu,r=t.children,i=n||"ul";return o.createElement(i,{ref:function(t){return e.el=t}},r)},t}(o.Component),a=l},5098:function(e){e.exports=function(){"use strict";const e={parentTrigger:"li",subMenu:"ul",toggle:!0,triggerElement:"a"},t={ACTIVE:"mm-active",COLLAPSE:"mm-collapse",COLLAPSED:"mm-collapsed",COLLAPSING:"mm-collapsing",METIS:"metismenu",SHOW:"mm-show"};class n{constructor(t,r){this.element=n.isElement(t)?t:document.querySelector(t),this.config=Object.assign(Object.assign({},e),r),this.disposed=!1,this.triggerArr=[],this.init()}static attach(e,t){return new n(e,t)}init(){const{METIS:e,ACTIVE:n,COLLAPSE:r}=t;this.element.classList.add(e),[].slice.call(this.element.querySelectorAll(this.config.subMenu)).forEach((e=>{e.classList.add(r);const t=e.closest(this.config.parentTrigger);(null==t?void 0:t.classList.contains(n))?this.show(e):this.hide(e);const o=null==t?void 0:t.querySelector(this.config.triggerElement);"true"!==(null==o?void 0:o.getAttribute("aria-disabled"))&&(null==o||o.setAttribute("aria-expanded","false"),null==o||o.addEventListener("click",this.clickEvent.bind(this)),this.triggerArr.push(o))}))}clickEvent(e){if(!this.disposed){const t=null==e?void 0:e.currentTarget;t&&"A"===t.tagName&&e.preventDefault();const n=t.closest(this.config.parentTrigger),r=null==n?void 0:n.querySelector(this.config.subMenu);this.toggle(r)}}update(){this.disposed=!1,this.init()}dispose(){this.triggerArr.forEach((e=>{e.removeEventListener("click",this.clickEvent.bind(this))})),this.disposed=!0}on(e,t,n){return this.element.addEventListener(e,t,n),this}off(e,t,n){return this.element.removeEventListener(e,t,n),this}emit(e,t,n=!1){const r=new CustomEvent(e,{bubbles:n,detail:t});this.element.dispatchEvent(r)}toggle(e){const n=e.closest(this.config.parentTrigger);(null==n?void 0:n.classList.contains(t.ACTIVE))?this.hide(e):this.show(e)}show(e){var n;const r=e,{ACTIVE:o,COLLAPSE:i,COLLAPSED:s,COLLAPSING:l,SHOW:a}=t;if(this.isTransitioning||r.classList.contains(l))return;const c=()=>{r.classList.remove(l),r.style.height="",r.removeEventListener("transitionend",c),this.setTransitioning(!1),this.emit("shown.metisMenu",{shownElement:r})},u=r.closest(this.config.parentTrigger);null==u||u.classList.add(o);const f=null==u?void 0:u.querySelector(this.config.triggerElement);null==f||f.setAttribute("aria-expanded","true"),null==f||f.classList.remove(s),r.style.height="0px",r.classList.remove(i),r.classList.remove(a),r.classList.add(l);const d=[].slice.call(null===(n=null==u?void 0:u.parentNode)||void 0===n?void 0:n.children).filter((e=>e!==u));this.config.toggle&&d.length>0&&d.forEach((e=>{const t=e.querySelector(this.config.subMenu);t&&this.hide(t)})),this.setTransitioning(!0),r.classList.add(i),r.classList.add(a),r.style.height=`${r.scrollHeight}px`,this.emit("show.metisMenu",{showElement:r}),r.addEventListener("transitionend",c)}hide(e){const{ACTIVE:n,COLLAPSE:r,COLLAPSED:o,COLLAPSING:i,SHOW:s}=t,l=e;if(this.isTransitioning||!l.classList.contains(s))return;this.emit("hide.metisMenu",{hideElement:l});const a=l.closest(this.config.parentTrigger);null==a||a.classList.remove(n);const c=()=>{l.classList.remove(i),l.classList.add(r),l.style.height="",l.removeEventListener("transitionend",c),this.setTransitioning(!1),this.emit("hidden.metisMenu",{hiddenElement:l})};l.style.height=`${l.getBoundingClientRect().height}px`,l.style.height=`${l.offsetHeight}px`,l.classList.add(i),l.classList.remove(r),l.classList.remove(s),this.setTransitioning(!0),l.addEventListener("transitionend",c),l.style.height="0px";const u=null==a?void 0:a.querySelector(this.config.triggerElement);null==u||u.setAttribute("aria-expanded","false"),null==u||u.classList.add(o)}setTransitioning(e){this.isTransitioning=e}static isElement(e){return Boolean(e.classList)}}return n}()},1210:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getDomainLocale=function(e,t,n,r){return!1};("function"===typeof t.default||"object"===typeof t.default&&null!==t.default)&&"undefined"===typeof t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8418:function(e,t,n){"use strict";var r=n(4941).Z;n(5753).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o,i=(o=n(7294))&&o.__esModule?o:{default:o},s=n(6273),l=n(2725),a=n(3462),c=n(1018),u=n(7190),f=n(1210),d=n(8684);var p="undefined"!==typeof i.default.useTransition,h={};function v(e,t,n,r){if(e&&s.isLocalURL(t)){e.prefetch(t,n,r).catch((function(e){0}));var o=r&&"undefined"!==typeof r.locale?r.locale:e&&e.locale;h[t+"%"+n+(o?"%"+o:"")]=!0}}var g=i.default.forwardRef((function(e,t){var n,o=e.href,g=e.as,m=e.children,y=e.prefetch,L=e.passHref,E=e.replace,b=e.shallow,C=e.scroll,O=e.locale,_=e.onClick,w=e.onMouseEnter,x=e.legacyBehavior,M=void 0===x?!0!==Boolean(!1):x,A=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,["href","as","children","prefetch","passHref","replace","shallow","scroll","locale","onClick","onMouseEnter","legacyBehavior"]);n=m,!M||"string"!==typeof n&&"number"!==typeof n||(n=i.default.createElement("a",null,n));var T=!1!==y,j=r(p?i.default.useTransition():[],2)[1],P=i.default.useContext(a.RouterContext),S=i.default.useContext(c.AppRouterContext);S&&(P=S);var k,I=i.default.useMemo((function(){var e=r(s.resolveHref(P,o,!0),2),t=e[0],n=e[1];return{href:t,as:g?s.resolveHref(P,g):n||t}}),[P,o,g]),N=I.href,R=I.as,D=i.default.useRef(N),H=i.default.useRef(R);M&&(k=i.default.Children.only(n));var q=M?k&&"object"===typeof k&&k.ref:t,B=r(u.useIntersection({rootMargin:"200px"}),3),U=B[0],z=B[1],V=B[2],W=i.default.useCallback((function(e){H.current===R&&D.current===N||(V(),H.current=R,D.current=N),U(e),q&&("function"===typeof q?q(e):"object"===typeof q&&(q.current=e))}),[R,q,N,V,U]);i.default.useEffect((function(){var e=z&&T&&s.isLocalURL(N),t="undefined"!==typeof O?O:P&&P.locale,n=h[N+"%"+R+(t?"%"+t:"")];e&&!n&&v(P,N,R,{locale:t})}),[R,N,z,O,T,P]);var K={ref:W,onClick:function(e){M||"function"!==typeof _||_(e),M&&k.props&&"function"===typeof k.props.onClick&&k.props.onClick(e),e.defaultPrevented||function(e,t,n,r,o,i,l,a,c){if("A"!==e.currentTarget.nodeName.toUpperCase()||!function(e){var t=e.currentTarget.target;return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)&&s.isLocalURL(n)){e.preventDefault();var u=function(){t[o?"replace":"push"](n,r,{shallow:i,locale:a,scroll:l})};c?c(u):u()}}(e,P,N,R,E,b,C,O,S?j:void 0)},onMouseEnter:function(e){M||"function"!==typeof w||w(e),M&&k.props&&"function"===typeof k.props.onMouseEnter&&k.props.onMouseEnter(e),s.isLocalURL(N)&&v(P,N,R,{priority:!0})}};if(!M||L||"a"===k.type&&!("href"in k.props)){var Z="undefined"!==typeof O?O:P&&P.locale,G=P&&P.isLocaleDomain&&f.getDomainLocale(R,Z,P.locales,P.domainLocales);K.href=G||d.addBasePath(l.addLocale(R,Z,P&&P.defaultLocale))}return M?i.default.cloneElement(k,K):i.default.createElement("a",Object.assign({},A,K),n)}));t.default=g,("function"===typeof t.default||"object"===typeof t.default&&null!==t.default)&&"undefined"===typeof t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7190:function(e,t,n){"use strict";var r=n(4941).Z;Object.defineProperty(t,"__esModule",{value:!0}),t.useIntersection=function(e){var t=e.rootRef,n=e.rootMargin,c=e.disabled||!s,u=o.useRef(),f=r(o.useState(!1),2),d=f[0],p=f[1],h=r(o.useState(null),2),v=h[0],g=h[1];o.useEffect((function(){if(s){if(u.current&&(u.current(),u.current=void 0),c||d)return;return v&&v.tagName&&(u.current=function(e,t,n){var r=function(e){var t,n={root:e.root||null,margin:e.rootMargin||""},r=a.find((function(e){return e.root===n.root&&e.margin===n.margin}));if(r&&(t=l.get(r)))return t;var o=new Map,i=new IntersectionObserver((function(e){e.forEach((function(e){var t=o.get(e.target),n=e.isIntersecting||e.intersectionRatio>0;t&&n&&t(n)}))}),e);return t={id:n,observer:i,elements:o},a.push(n),l.set(n,t),t}(n),o=r.id,i=r.observer,s=r.elements;return s.set(e,t),i.observe(e),function(){if(s.delete(e),i.unobserve(e),0===s.size){i.disconnect(),l.delete(o);var t=a.findIndex((function(e){return e.root===o.root&&e.margin===o.margin}));t>-1&&a.splice(t,1)}}}(v,(function(e){return e&&p(e)}),{root:null==t?void 0:t.current,rootMargin:n})),function(){null==u.current||u.current(),u.current=void 0}}if(!d){var e=i.requestIdleCallback((function(){return p(!0)}));return function(){return i.cancelIdleCallback(e)}}}),[v,c,n,t,d]);var m=o.useCallback((function(){p(!1)}),[]);return[g,d,m]};var o=n(7294),i=n(9311),s="function"===typeof IntersectionObserver;var l=new Map,a=[];("function"===typeof t.default||"object"===typeof t.default&&null!==t.default)&&"undefined"===typeof t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1018:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FullAppTreeContext=t.AppTreeContext=t.AppRouterContext=void 0;var r,o=(r=n(7294))&&r.__esModule?r:{default:r};var i=o.default.createContext(null);t.AppRouterContext=i;var s=o.default.createContext(null);t.AppTreeContext=s;var l=o.default.createContext(null);t.FullAppTreeContext=l},9843:function(){},1664:function(e,t,n){e.exports=n(8418)},1163:function(e,t,n){e.exports=n(387)},8357:function(e,t,n){"use strict";n.d(t,{w_:function(){return c}});var r=n(7294),o={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=r.createContext&&r.createContext(o),s=function(){return s=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},s.apply(this,arguments)},l=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};function a(e){return e&&e.map((function(e,t){return r.createElement(e.tag,s({key:t},e.attr),a(e.child))}))}function c(e){return function(t){return r.createElement(u,s({attr:s({},e.attr)},t),a(e.child))}}function u(e){var t=function(t){var n,o=e.attr,i=e.size,a=e.title,c=l(e,["attr","size","title"]),u=i||t.size||"1em";return t.className&&(n=t.className),e.className&&(n=(n?n+" ":"")+e.className),r.createElement("svg",s({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,o,c,{className:n,style:s(s({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),a&&r.createElement("title",null,a),e.children)};return void 0!==i?r.createElement(i.Consumer,null,(function(e){return t(e)})):t(o)}},7568:function(e,t,n){"use strict";function r(e,t,n,r,o,i,s){try{var l=e[i](s),a=l.value}catch(c){return void n(c)}l.done?t(a):Promise.resolve(a).then(r,o)}function o(e){return function(){var t=this,n=arguments;return new Promise((function(o,i){var s=e.apply(t,n);function l(e){r(s,o,i,l,a,"next",e)}function a(e){r(s,o,i,l,a,"throw",e)}l(void 0)}))}}n.d(t,{Z:function(){return o}})}}]);