(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1205],{5547:function(e,s,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/user-panel/payment-list",function(){return n(3046)}])},3401:function(e,s,n){"use strict";n.d(s,{O:function(){return t}});var a=n(5893),t=(n(7294),function(e){var s=e.heading;return(0,a.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] pb-2",children:s})})},3046:function(e,s,n){"use strict";n.r(s);var a=n(5893),t=(n(7294),n(3652)),i=n(3401),l=n(1664),r=n.n(l);s.default=function(){return(0,a.jsx)(t.Z,{children:(0,a.jsxs)("div",{className:"bg-yellow-50 bg-opacity-20 h-auto m-6 w-[90%] text-[16px] p-4 rounded",children:[(0,a.jsx)(i.O,{heading:"Trip History"}),(0,a.jsx)("div",{children:(0,a.jsxs)("div",{children:["In this page, the user has access to all of his / her payments and information about their current status:",(0,a.jsxs)("ul",{className:"list-disc",children:[(0,a.jsxs)("li",{children:[(0,a.jsx)("span",{className:"font-bold",children:"Trip Id: "}),"The trip that the payment is related to. The user will be redirected to the ",(0,a.jsx)(r(),{href:"/user-panel/trip-details",children:(0,a.jsx)("a",{className:"underline",children:"trip-details"})})," page by clicking on the Trip ID."]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("span",{className:"font-bold",children:"Transaction Id: "})," The payment's transaction id"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("span",{className:"font-bold",children:"Driver Name: "})," The name of the driver who took the trip"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("span",{className:"font-bold",children:"Driver Email: "}),"The driver's email address"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("span",{className:"font-bold",children:"Date: "}),"Date of the trip."]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("span",{className:"font-bold",children:"Time: "}),"Time of the trip."]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("span",{className:"font-bold",children:"Distance: "}),"The total distance of the trip."]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("span",{className:"font-bold",children:"Total Fare: "}),"The total cost of the trip."]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("span",{className:"font-bold",children:"Paid: "}),"The amount paid by the user."]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("span",{className:"font-bold",children:"payment_method: "}),"The method of the payment"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("span",{className:"font-bold",children:"Payment Status: "}),"The current status of the payment. Several statuses may occur, including:",(0,a.jsxs)("ul",{className:"list-disc",children:[(0,a.jsxs)("li",{children:[(0,a.jsx)("span",{className:"text-green-500 font-semi-bold",children:"Success: "})," The transaction was successful."]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("span",{className:"text-red-500 font-semi-bold",children:"Failed: "}),"The transaction has failed."]})]})]})]})]})}),(0,a.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/user-panel/payment-history.png",alt:""})]})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return s=5547,e(e.s=s);var s}));var s=e.O();_N_E=s}]);