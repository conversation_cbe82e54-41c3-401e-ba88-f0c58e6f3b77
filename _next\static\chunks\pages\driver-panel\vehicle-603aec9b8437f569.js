(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7558],{8917:function(e,t,i){(window.__NEXT_P=window.__NEXT_P||[]).push(["/driver-panel/vehicle",function(){return i(6919)}])},3401:function(e,t,i){"use strict";i.d(t,{O:function(){return a}});var n=i(5893),a=(i(7294),function(e){var t=e.heading;return(0,n.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] pb-2",children:t})})},6919:function(e,t,i){"use strict";i.r(t);var n=i(5893),a=(i(7294),i(3652)),r=i(3401);t.default=function(){return(0,n.jsx)(a.Z,{children:(0,n.jsxs)("div",{className:"bg-yellow-50 bg-opacity-20 h-auto m-6 w-[90%] text-[16px] p-4 rounded",children:[(0,n.jsx)(r.O,{heading:"Vehicles"}),(0,n.jsx)("p",{children:"Vehicle page shows the detailed information about the vehicle that the driver registered with"}),(0,n.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/driver-panel/vehicle.png",alt:""}),(0,n.jsx)("p",{className:"font-bold mt-5",children:"Update Vehicle Information: "}),(0,n.jsxs)("p",{children:["The user will be redirected to another page by clicking the ",(0,n.jsx)("strong",{children:"Update Vehicle"})," button. The page has already been filled form with the previous information. If any modification is needed, the user can make it and submit the form by clicking ",(0,n.jsx)("strong",{children:"Update"})," button"]}),(0,n.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/driver-panel/update-vehicle.png",alt:""})]})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return t=8917,e(e.s=t);var t}));var t=e.O();_N_E=t}]);