(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1333],{4884:function(e,a,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/feedback",function(){return n(3475)}])},3475:function(e,a,n){"use strict";n.r(a);var s=n(5893),t=(n(7294),n(3652));a.default=function(){return(0,s.jsx)("div",{children:(0,s.jsx)(t.Z,{children:(0,s.jsx)("div",{className:"bg-yellow-50 bg-opacity-20 h-auto m-6 w-[90%] text-[16px] p-4 rounded",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] pb-2",children:"User Feedback"}),(0,s.jsx)("p",{className:"text-base mt-2",children:"Here, the admin can view all feedback given by users. The administrator can approve or block it by changing its status and can also delete any feedback using the action buttons on the right side."}),(0,s.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/admin/feedback/user-feedback.png",alt:""})]})})})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return a=4884,e(e.s=a);var a}));var a=e.O();_N_E=a}]);