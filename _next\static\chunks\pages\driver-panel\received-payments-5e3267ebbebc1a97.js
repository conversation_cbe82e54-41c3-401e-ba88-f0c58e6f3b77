(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7329],{8178:function(e,n,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/driver-panel/received-payments",function(){return t(972)}])},3401:function(e,n,t){"use strict";t.d(n,{O:function(){return r}});var a=t(5893),r=(t(7294),function(e){var n=e.heading;return(0,a.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] pb-2",children:n})})},972:function(e,n,t){"use strict";t.r(n);var a=t(5893),r=(t(7294),t(3401)),i=t(3652);n.default=function(){return(0,a.jsx)(i.Z,{children:(0,a.jsxs)("div",{className:"bg-yellow-50 bg-opacity-20 h-auto m-6 w-[90%] text-[16px] p-4 rounded",children:[(0,a.jsx)(r.O,{heading:"Received Payments"}),(0,a.jsx)("p",{children:"The table shows the a total payment associated with a trip"}),(0,a.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/driver-panel/received-payments.png",alt:""})]})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return n=8178,e(e.s=n);var n}));var n=e.O();_N_E=n}]);