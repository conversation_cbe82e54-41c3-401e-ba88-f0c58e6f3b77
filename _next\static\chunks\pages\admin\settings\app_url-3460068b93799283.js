(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7411],{8617:function(s,e,i){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/settings/app_url",function(){return i(1946)}])},1946:function(s,e,i){"use strict";i.r(e);var t=i(5893);i(7294);e.default=function(){return(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:"Application URL"}),(0,t.jsx)("div",{className:"m-4",children:(0,t.jsxs)("ul",{className:"list-disc",children:[(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Part 1 : Application Url"}),"Link android and ios app to your website.",(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/application_url/appUrl.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Part 2 : Site Url"}),"site url is the url of your website.",(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/application_url/sitesetting.png",alt:""})]})]})})]})}}},function(s){s.O(0,[9774,2888,179],(function(){return e=8617,s(s.s=e);var e}));var e=s.O();_N_E=e}]);