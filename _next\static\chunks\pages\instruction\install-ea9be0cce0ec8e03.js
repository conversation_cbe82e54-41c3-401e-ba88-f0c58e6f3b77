(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8745],{7453:function(e,s,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/instruction/install",function(){return n(3856)}])},3856:function(e,s,n){"use strict";n.r(s);var t=n(5893),l=n(1664),i=n.n(l),r=(n(7294),n(3652));s.default=function(){return(0,t.jsx)(r.Z,{children:(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-center",children:"Installing Process"}),(0,t.jsxs)("div",{className:"my-4",children:[(0,t.jsx)("h5",{className:"border-b-[1px] border-black pb-2",children:"Install"}),(0,t.jsxs)("div",{className:"m-4",children:[(0,t.jsx)("h6",{className:"text-red-600 border-b-[1px] border-red-600 text-center pb-2",children:"** To setup this project, you need to make sure that your hosting server supports node.js **"}),(0,t.jsxs)("ul",{className:"list-disc",children:[(0,t.jsxs)("li",{className:"my-4",children:["First, You have to download the file, you will get a zip file.",(0,t.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/i1.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:["Then, you have to ",(0,t.jsx)("span",{className:"text-[20px] font-bold text-red-500",children:"Extract"})," the file.",(0,t.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/i2.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:["You will find two files ",(0,t.jsx)("span",{className:"text-[20px] font-bold text-red-500",children:"1. taxstick-backend"})," and ",(0,t.jsx)("span",{className:"text-[20px] font-bold text-red-500",children:"2. taxstick-frondend"}),".",(0,t.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/i3.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:["Install project dependencies:",(0,t.jsxs)("ul",{className:"list-disc",children:[(0,t.jsxs)("li",{children:[" ",(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)("p",{className:"mb-0",children:" If NodeJs is not installed, then follow this step to install NodeJs:"}),(0,t.jsx)("p",{className:"mb-0",children:(0,t.jsx)(i(),{href:"https://nodejs.org/en/",children:(0,t.jsx)("a",{target:"_blank",rel:"noopener noreferrer",children:"https://nodejs.org/en/"})})})]})]}),(0,t.jsxs)("li",{children:["After successfully installed, check NodeJs version. It will show some number.",(0,t.jsx)("span",{className:"text-red-500 font-medium",children:" node --version"})]}),(0,t.jsxs)("li",{children:["If yarn is not installed, then use the following command to install yarn:",(0,t.jsx)("span",{className:"text-red-500 font-medium",children:" npm i -g yarn"})]}),(0,t.jsxs)("li",{children:["Check yarn version, It will show some number.",(0,t.jsx)("span",{className:"text-red-500 font-medium",children:" yarn --version"})]})]})]}),(0,t.jsx)("p",{className:"text-center",children:"Now you need to follow the below instructions"}),(0,t.jsxs)("div",{className:"flex justify-center items-center gap-4",children:[(0,t.jsx)("div",{className:"h-10 w-72 flex justify-center items-center rounded bg-[#D22F25]",children:(0,t.jsx)(i(),{href:"/instruction/backend",children:(0,t.jsx)("a",{className:"text-white text-lg font-bold",children:"Backend Installing Process"})})}),(0,t.jsx)("div",{className:"h-10 w-72 flex justify-center items-center rounded bg-[#D22F25]",children:(0,t.jsx)(i(),{href:"/instruction/frontend",children:(0,t.jsx)("a",{className:"text-white text-lg font-bold",children:"Frontend Installing Process"})})})]}),(0,t.jsx)("p",{className:"text-right text-red-500 font-medium m-6",children:"***If you face any kind of installation problem, contact our support***"})]})]})]})]})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return s=7453,e(e.s=s);var s}));var s=e.O();_N_E=s}]);