(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7239],{7563:function(e,n,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/live/termsCondition",function(){return s(8576)}])},8576:function(e,n,s){"use strict";s.r(n);var t=s(5893),i=(s(7294),s(3652));n.default=function(){return(0,t.jsx)(i.Z,{children:(0,t.jsxs)("div",{className:"bg-zinc-100 h-auto m-6 w-[90%] text-[16px] p-4",children:[(0,t.jsx)("h4",{className:"text-center font-semibold underline",children:"Terms & Condition Page"}),(0,t.jsxs)("div",{className:"my-4",children:[(0,t.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"Terms & Condition"}),(0,t.jsxs)("div",{className:"m-4",children:[(0,t.jsx)("p",{className:"text-base",children:"In this dynamic page, anyone can see a terms & condition page title, subtitle and some text about the terms & condition."}),(0,t.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/homepage/etc/terms-condition.png",alt:""})]})]})]})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return n=7563,e(e.s=n);var n}));var n=e.O();_N_E=n}]);