<!DOCTYPE html><html><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width"/><meta name="next-head-count" content="2"/><link rel="preload" href="/_next/static/css/ef96b46dffe113f8.css" as="style"/><link rel="stylesheet" href="/_next/static/css/ef96b46dffe113f8.css" data-n-g=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="/_next/static/chunks/polyfills-0d1b80a048d4787e.js"></script><script src="/_next/static/chunks/webpack-a146a8ef8f1e9d05.js" defer=""></script><script src="/_next/static/chunks/framework-dfb4b001ca8efcfd.js" defer=""></script><script src="/_next/static/chunks/main-9871903446ae15d0.js" defer=""></script><script src="/_next/static/chunks/pages/_app-e20749b88fa2b6ce.js" defer=""></script><script src="/_next/static/chunks/pages/admin/settings/email-d9b9050af644b986.js" defer=""></script><script src="/_next/static/KDIus1qkIHYmvN8aySE_u/_buildManifest.js" defer=""></script><script src="/_next/static/KDIus1qkIHYmvN8aySE_u/_ssgManifest.js" defer=""></script></head><body><div id="__next"><div><h4 class="text-center border-b-[1px] py-2 border-black">Email settings</h4><div class="m-4"><p class="text-[18px] font-semibold">To send email from the system,email credential need to be full-fill . There are three email option to chose from</p><p class="text-[18px] font-bold text-green-600">** Select the default email. Email always send from default email **<!-- --> </p><ul class="list-disc"><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">SendGrid Smtp</p>sendgrid is a cloud-based email service that provides reliable transactional and marketing email delivery. It is a great option for sending emails from your application. It is a great option for sending emails from your application. Go to<!-- --> <a href="https://sendgrid.com/" target="_blank" rel="noreferrer" class="text-blue-500">https://sendgrid.com/</a> <!-- -->and create an account.<p class="text-[18px] font-semibold text-green-500">After creating an account, you will get an API key. Copy the API key and paste it in the API key field.</p><img class=" w-auto mt-4" src="/images/setting/email/sendgrid.png" alt=""/></li><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Gmail Provider</p>Gmail is a free email service provided by Google. It is a great option for sending emails from your application.<img class=" w-auto mt-4" src="/images/setting/email/gmail.png" alt=""/></li><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Other Provider</p>other provider can be used to send email from the system.<img class=" w-auto mt-4" src="/images/setting/email/other.png" alt=""/></li></ul></div></div></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{}},"page":"/admin/settings/email","query":{},"buildId":"KDIus1qkIHYmvN8aySE_u","nextExport":true,"autoExport":true,"isFallback":false,"scriptLoader":[]}</script></body></html>