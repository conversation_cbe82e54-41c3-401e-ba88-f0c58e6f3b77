(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[774],{4582:function(e,a,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/report",function(){return r(7699)}])},7699:function(e,a,r){"use strict";r.r(a);var t=r(5893),s=(r(7294),r(3652));a.default=function(){return(0,t.jsx)("div",{children:(0,t.jsx)(s.Z,{children:(0,t.jsxs)("div",{className:"bg-yellow-50 bg-opacity-20 h-auto m-6 w-[90%] text-[16px] p-4 rounded",children:[(0,t.jsx)("h4",{className:"text-center font-semibold underline",children:"Report"}),(0,t.jsx)("p",{className:"text-base text-center",children:"To get any report, you have to select start date and end date firstly."}),(0,t.jsx)("img",{className:"h-auto w-auto mx-auto mt-2 shadow-sm",src:"/images/admin/report/get-report.png",alt:""}),(0,t.jsxs)("div",{className:"mt-5",children:[(0,t.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] pb-2",children:"User Report"}),(0,t.jsx)("p",{className:"text-base mt-2",children:"Here, the admin can view all user report according to the date range."}),(0,t.jsx)("img",{className:"h-auto w-auto mx-auto mt-2 shadow-sm",src:"/images/admin/report/user-report.png",alt:""})]}),(0,t.jsxs)("div",{className:"my-6",children:[(0,t.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] pb-2",children:"Driver Report"}),(0,t.jsx)("p",{className:"text-base mt-2",children:"Here, the admin can view all driver earning report according to the date range."}),(0,t.jsx)("img",{className:"h-auto w-auto mx-auto mt-2 shadow-sm",src:"/images/admin/report/driver-report.png",alt:""})]}),(0,t.jsxs)("div",{className:"my-6",children:[(0,t.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] pb-2",children:"Company Report"}),(0,t.jsx)("p",{className:"text-base mt-2",children:"Here, the admin can view all of the reports of - registered user, registered driver, total vehicle, total payment, total withdraw and company revenue according to the date ranges."}),(0,t.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/admin/report/company-report.png",alt:""})]})]})})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return a=4582,e(e.s=a);var a}));var a=e.O();_N_E=a}]);