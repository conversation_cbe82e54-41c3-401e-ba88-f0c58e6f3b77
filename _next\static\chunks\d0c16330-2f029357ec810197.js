"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8907],{7106:function(t,a,r){r.d(a,{ctA:function(){return h},tJc:function(){return n}});var e=r(8357);function h(t){return(0,e.w_)({tag:"svg",attr:{viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor",fill:"none",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"desc",attr:{},child:[]},{tag:"path",attr:{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}},{tag:"path",attr:{d:"M13 5h8"}},{tag:"path",attr:{d:"M13 9h5"}},{tag:"path",attr:{d:"M13 15h8"}},{tag:"path",attr:{d:"M13 19h5"}},{tag:"rect",attr:{x:"3",y:"4",width:"6",height:"6",rx:"1"}},{tag:"rect",attr:{x:"3",y:"14",width:"6",height:"6",rx:"1"}}]})(t)}function n(t){return(0,e.w_)({tag:"svg",attr:{viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor",fill:"none",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"desc",attr:{},child:[]},{tag:"path",attr:{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}},{tag:"path",attr:{d:"M8 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h5.697"}},{tag:"path",attr:{d:"M18 12v-5a2 2 0 0 0 -2 -2h-2"}},{tag:"rect",attr:{x:"8",y:"3",width:"6",height:"4",rx:"2"}},{tag:"path",attr:{d:"M8 11h4"}},{tag:"path",attr:{d:"M8 15h3"}},{tag:"circle",attr:{cx:"16.5",cy:"17.5",r:"2.5"}},{tag:"path",attr:{d:"M18.5 19.5l2.5 2.5"}}]})(t)}}}]);