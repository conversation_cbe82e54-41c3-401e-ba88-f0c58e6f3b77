(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9247],{7742:function(e,s,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/settings/whatsapp",function(){return t(256)}])},256:function(e,s,t){"use strict";t.r(s);var a=t(5893);t(7294);s.default=function(){return(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:"WhatsApp settings"}),(0,a.jsxs)("div",{className:"m-4",children:[(0,a.jsx)("p",{className:"text-[18px] font-semibold",children:"To send whatsapp messages from the system, fill up credential ."}),(0,a.jsx)("ul",{className:"list-disc",children:(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Twillo Provider"}),"Twillo also provides whatsapp service. It is a great option for sending whatsapp messages from your application.",(0,a.jsxs)("p",{className:"text-[18px] font-semibold text-green-500",children:["Go to"," ",(0,a.jsx)("a",{href:"https://www.twilio.com/",target:"_blank",rel:"noreferrer",className:"text-blue-500",children:"https://www.twilio.com/"})," ","and create an account."]}),(0,a.jsx)("p",{className:"text-[18px] font-semibold text-green-500",children:"After creating an account, you will get an API key. Copy the API key and paste it in the API key field."}),(0,a.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/wa/wa.png",alt:""})]})})]})]})}}},function(e){e.O(0,[9774,2888,179],(function(){return s=7742,e(e.s=s);var s}));var s=e.O();_N_E=s}]);