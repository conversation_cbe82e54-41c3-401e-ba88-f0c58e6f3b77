(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9739],{8056:function(e,s,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/instruction/setting/db",function(){return a(1503)}])},1503:function(e,s,a){"use strict";a.r(s);var t=a(5893),n=a(1664),o=a.n(n),c=a(1163),i=(a(7294),a(3750)),l=a(3652);s.default=function(){var e=(0,c.useRouter)();return(0,t.jsx)(l.Z,{children:(0,t.jsxs)("div",{className:"bg-gray-50 h-auto m-6 w-[90%] text-[16px] p-4",children:[(0,t.jsxs)("p",{onClick:function(){e.push("/instruction/setting/")},className:"flex items-center gap-2 cursor-pointer text-red-600",children:[(0,t.jsx)("span",{className:"mt-1",children:(0,t.jsx)(i.R_q,{})})," Back"]}),(0,t.jsx)("h4",{className:"text-center",children:"MongoDB database Setup Information"}),(0,t.jsxs)("div",{className:"my-4",children:[(0,t.jsx)("h5",{className:"border-b-[1px] border-black capitalize",children:"Database setup"}),(0,t.jsx)("div",{className:"m-4 ",children:(0,t.jsxs)("div",{children:["Next, you have to setup the database. Here, we are suggesting to create a MongoDB database in MongoDB Atlas. Also, you can create database on your server too. Now follow the below steps.",(0,t.jsxs)("ul",{className:"list-disc",children:[(0,t.jsxs)("li",{children:["At first, you have to create a MongoDB Atlas account. ",(0,t.jsx)(o(),{href:"https://www.mongodb.com/atlas/database",children:(0,t.jsx)("a",{children:"Click here"})})," to create account.You can choose any plan, also you can choose ",(0,t.jsx)("span",{className:"text-red-500",children:"free shared"})," plan too.",(0,t.jsx)("img",{className:"h-auto w-auto my-4",src:"/images/ndb1.png",alt:""})]}),(0,t.jsxs)("li",{children:["Make sure you have to select free shared and hit enter ",(0,t.jsx)("span",{className:"text-red-500",children:"Create Clustor"}),(0,t.jsx)("img",{className:"h-auto w-auto my-4",src:"/images/ndb2.png",alt:""})]}),(0,t.jsxs)("li",{children:["Then you can see your personal MongoDB database dashboard.Click ",(0,t.jsx)("span",{className:"text-red-500",children:"Build a Database"})," button.",(0,t.jsx)("img",{className:"h-auto w-auto my-4",src:"/images/ndb0.png",alt:""})]}),(0,t.jsxs)("li",{children:["Please fill in this information carefully. You need to follow these picture instructions.",(0,t.jsx)("img",{className:"h-auto w-auto my-4",src:"/images/ndb3.png",alt:""})]}),(0,t.jsxs)("li",{children:["Now Click ",(0,t.jsx)("span",{className:"text-red-500",children:"Network access."})," and Click ",(0,t.jsx)("span",{className:"text-red-500",children:"ADD IP ADDRESS"}),", then you can see a modal.",(0,t.jsx)("img",{className:"h-auto w-auto my-4",src:"/images/ndb4a.png",alt:""})]}),(0,t.jsxs)("li",{children:["Now you can see of your mondoDB account dashboard. You have to create Network Access. We recommend to put your server IP address to maintain the security. Also you can set it public access, which is not recommended.",(0,t.jsx)("img",{className:"h-auto w-auto my-4",src:"/images/ndb4.png",alt:""})]}),(0,t.jsxs)("li",{children:["Now delete the previous IP address. Keep the new one.",(0,t.jsx)("img",{className:"h-auto w-auto my-4",src:"/images/ndb5.png",alt:""})]}),(0,t.jsxs)("li",{children:["Then you need to go to the main dashboard of the database. And click on ",(0,t.jsx)("span",{className:"text-red-500",children:"Connect"}),(0,t.jsx)("img",{className:"h-auto w-auto my-4",src:"/images/ndb6.png",alt:""})]}),(0,t.jsxs)("li",{children:["Now select ",(0,t.jsx)("span",{className:"text-red-500",children:"connect your application."}),(0,t.jsx)("img",{className:"h-auto w-auto my-4",src:"/images/ndb6a.png",alt:""})]}),(0,t.jsxs)("li",{children:["Now copy the link carefully.",(0,t.jsx)("img",{className:"h-auto w-auto my-4",src:"/images/ndb7.png",alt:""})]}),(0,t.jsxs)("li",{children:["In the link below, you need to enter your username, password and a database name of your choice as shown in the given image.",(0,t.jsx)("img",{className:"h-auto w-auto my-4",src:"/images/ndb8.png",alt:""})]}),(0,t.jsxs)("li",{children:["Right now, Your Database creation process is done. Just copy the URL from your mongoDB atlas account like the upper image. Now you have to paste the URL in ",(0,t.jsx)("span",{className:"text-red-500",children:"Database String"})," field. Also you have to put company website name like taxstick."]}),(0,t.jsx)("img",{className:"h-auto w-auto my-2",src:"/images/s2.png",alt:""})]})]})})]})]})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return s=8056,e(e.s=s);var s}));var s=e.O();_N_E=s}]);