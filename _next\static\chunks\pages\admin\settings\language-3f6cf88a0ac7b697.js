(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9089],{4510:function(e,a,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/settings/language",function(){return s(6415)}])},6415:function(e,a,s){"use strict";s.r(a);var t=s(5893);s(7294);a.default=function(){return(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:"Language Setting"}),"This page is used to set the language of your website.You can see all the available languages and their status. you can add new language and set the default language.You can also delete the language.",(0,t.jsx)("div",{className:"m-4",children:(0,t.jsxs)("ul",{className:"list-disc",children:[(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Part 1 : Language & Translation Page"}),"This page is used to set the language of your website.You can see all the available languages and their status. you can add new language and set the default.Also you can delete the language.",(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/language/1.%20language_translation.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Part 2 : Add Language"}),"To add a new language, click on the add language button. Enter the language name and select the flag. rtl is used for right to left language like Arabic, Persian, Urdu etc.",(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/language/2.add%20language.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Part 3 : Status"}),"Status is used to enable or disable the language.Only the enabled language will be shown in the System.",(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/language/3.status.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Part 4: Default"}),"Only one language can be set as default. When a new user visits the website, the default language will be shown.",(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/language/4.default.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Part 5: Edit & Delete"}),"You can edit or delete the language from here.",(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/language/5.edit_del.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Part 6: Translation"}),"You can translate the language from here. Enter the value in the text box and click on the save button.",(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/language/6.translation.png",alt:""})]})]})})]})}}},function(e){e.O(0,[9774,2888,179],(function(){return a=4510,e(e.s=a);var a}));var a=e.O();_N_E=a}]);