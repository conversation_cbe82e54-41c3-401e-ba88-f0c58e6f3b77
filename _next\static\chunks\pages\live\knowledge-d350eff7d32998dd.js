(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4265],{8344:function(e,n,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/live/knowledge",function(){return s(9004)}])},9004:function(e,n,s){"use strict";s.r(n);var a=s(5893),t=(s(7294),s(3652));n.default=function(){return(0,a.jsx)(t.Z,{children:(0,a.jsxs)("div",{className:"bg-zinc-100 h-auto m-6 w-[90%] text-[16px] p-4",children:[(0,a.jsx)("h4",{className:"text-center font-semibold underline",children:"Knowledge Page"}),(0,a.jsxs)("div",{className:"my-4",children:[(0,a.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"Knowledge"}),(0,a.jsxs)("div",{className:"m-4",children:[(0,a.jsx)("p",{className:"text-base",children:"In this dynamic page, anyone can see a knowledge-base page title, subtitle and some content(question and answer)."}),(0,a.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/homepage/etc/knowledge.png",alt:""})]})]})]})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return n=8344,e(e.s=n);var n}));var n=e.O();_N_E=n}]);