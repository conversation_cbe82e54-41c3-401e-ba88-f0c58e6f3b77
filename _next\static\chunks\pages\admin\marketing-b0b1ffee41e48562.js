(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4556],{3574:function(e,s,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/marketing",function(){return a(6962)}])},6962:function(e,s,a){"use strict";a.r(s);var i=a(5893),t=(a(7294),a(3652)),l=a(5236);s.default=function(){return(0,i.jsx)(t.Z,{children:(0,i.jsx)("div",{className:"bg-yellow-50 bg-opacity-20 h-auto m-6 w-[90%] text-[16px] p-4 rounded",children:(0,i.jsxs)(l.Z,{defaultActiveKey:"1",centered:!0,children:[(0,i.jsxs)(l.<PERSON><PERSON>,{tab:"SMS Options",children:[(0,i.jsx)("h4",{className:"text-center font-semibold underline",children:"SMS Options"}),(0,i.jsx)("p",{className:"text-base",children:"Here you will see a form. You can send a message to an individual person or in a group using this form. By clicking the send now or schedule for later button, you can send a message immediately or later."}),(0,i.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"Send sms"}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/marketing/send-sms.png",alt:""}),(0,i.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"another page"}),(0,i.jsxs)("ul",{className:"list-disc text-base",children:[(0,i.jsx)("li",{children:(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"font-medium text-[#FFA525]",children:"All SMS :"})," Here, you will see the list of all successful, pending, or failed SMS."]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"text-base font-medium text-[#FFA525]",children:"Pending SMS :"})," Here, you will see the list of all pending SMS."]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"text-base font-medium text-[#FFA525]",children:"Delivered SMS :"})," Here, you will see the list of all successful SMS."]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"text-base font-medium text-[#FFA525]",children:"Schedule SMS :"})," Here, you will see the list of all SMS that are scheduled for later."]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"text-base font-medium text-[#FFA525]",children:"Failed SMS :"})," Here, you will see the list of all failed SMS."]})})]})]},"1"),(0,i.jsxs)(l.Z.TabPane,{tab:"Whatsapp Options",children:[(0,i.jsx)("h4",{className:"text-center font-semibold underline",children:"Whatsapp Options"}),(0,i.jsx)("p",{className:"text-base",children:"Here you will see a form. You can send a message to an individual person or in a group using this form. By clicking the send now or schedule for later button, you can send a message immediately or later."}),(0,i.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"Send Whatsapp Message"}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/marketing/send-whatsapp.png",alt:""}),(0,i.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"another page"}),(0,i.jsxs)("ul",{className:"list-disc text-base",children:[(0,i.jsx)("li",{children:(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"font-medium text-[#FFA525]",children:"All Whatsapp Message :"})," Here, you will see the list of all successful, pending, or failed Message."]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"text-base font-medium text-[#FFA525]",children:"Pending Whatsapp Message :"})," Here, you will see the list of all pending Message."]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"text-base font-medium text-[#FFA525]",children:"Delivered Whatsapp Message :"})," Here, you will see the list of all successful Message."]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"text-base font-medium text-[#FFA525]",children:"Schedule Whatsapp Message :"})," Here, you will see the list of all Message that are scheduled for later."]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"text-base font-medium text-[#FFA525]",children:"Failed Whatsapp Message :"})," Here, you will see the list of all failed Message."]})})]})]},"2"),(0,i.jsxs)(l.Z.TabPane,{tab:"Email Options",children:[(0,i.jsx)("h4",{className:"text-center font-semibold underline",children:"Email Options Page"}),(0,i.jsx)("p",{className:"text-base",children:"Here you will see a form. You can send a email to an individual person, in a group or subscribed user using this form. By clicking the send now or schedule for later button, you can send a email immediately or later."}),(0,i.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"Send Email"}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/marketing/send-email.png",alt:""}),(0,i.jsxs)("p",{className:"text-base mt-4",children:["If you want to send email to an individual person, you have to enter his/her email address only. On the other hand, you can send email to a specific group by selecting a group or by clicking the ",(0,i.jsx)("span",{className:"font-medium text-[#FFA525]",children:"Send to Subscribed User"})," checkbox to send email to the subscribed user."]}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm mx-auto",src:"/images/admin/marketing/email-group.png",alt:""}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm mt-5 mx-auto",src:"/images/admin/marketing/email-tem.png",alt:""}),(0,i.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"another page"}),(0,i.jsxs)("ul",{className:"list-disc text-base",children:[(0,i.jsx)("li",{children:(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"font-medium text-[#FFA525]",children:"All Email :"})," Here, you will see the list of all successful, pending, or failed Email."]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"text-base font-medium text-[#FFA525]",children:"Pending Email :"})," Here, you will see the list of all pending Email."]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"text-base font-medium text-[#FFA525]",children:"Delivered Email :"})," Here, you will see the list of all successful Email."]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"text-base font-medium text-[#FFA525]",children:"Schedule Email :"})," Here, you will see the list of all Email that are scheduled for later."]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"text-base font-medium text-[#FFA525]",children:"Failed Email :"})," Here, you will see the list of all failed Email."]})})]})]},"3"),(0,i.jsxs)(l.Z.TabPane,{tab:"Manage User",children:[(0,i.jsx)("h4",{className:"text-center font-semibold underline",children:"Manage User Page"}),(0,i.jsxs)("div",{className:"my-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"All Users"}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsx)("p",{className:"text-base",children:"Here, you will see a list of all users, drivers, and employees involved in this platform."}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/marketing/all-user.png",alt:""})]})]}),(0,i.jsxs)("div",{className:"my-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"Active Users"}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsx)("p",{className:"text-base",children:"Here, you will see a list of all active users involved in this platform. You can ban any user from the list by using the action button on the right side."}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/marketing/active-user.png",alt:""})]})]}),(0,i.jsxs)("div",{className:"my-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"Banned Users"}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsx)("p",{className:"text-base",children:"Here, you will see a list of all banned users involved in this platform. You can activate any user from the list by using the action button on the right side."}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/marketing/banned-user.png",alt:""})]})]}),(0,i.jsxs)("div",{className:"my-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"Subscribed Users"}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsxs)("p",{className:"text-base",children:["Here, you will see a list of all users who have subscribed to this platform by using the landing page newsletter form. You can ban or activate any user from the list by using the action button on the right side. You can also add a subscribed user manually by clicking on the ",(0,i.jsx)("span",{className:"text-[#FFA525] font-semibold",children:"subscribe to our newsletter"})," button."]}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/marketing/subscribed-user.png",alt:""})]})]})]},"4"),(0,i.jsxs)(l.Z.TabPane,{tab:"Manage Group",children:[(0,i.jsx)("h4",{className:"text-center font-semibold underline",children:"Manage Group Page"}),(0,i.jsx)("p",{className:"text-base text-center",children:"Here you will see user sms group list. See the screenshot below and follow the process."}),(0,i.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"User Sms Groups"}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/marketing/manage-group.png",alt:""}),(0,i.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"another page"}),(0,i.jsxs)("ul",{className:"list-disc text-base",children:[(0,i.jsx)("li",{children:(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"font-medium text-[#FFA525]",children:"User Whatsapp Groups :"})," Follow the same process as the ",(0,i.jsx)("span",{className:"font-semibold",children:"User Sms Groups"}),"."]})}),(0,i.jsx)("li",{children:(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"text-base font-medium text-[#FFA525]",children:"User Email Groups :"})," Follow the same process as the User ",(0,i.jsx)("span",{className:"font-semibold",children:"User Sms Groups"}),"."]})})]})]},"5"),(0,i.jsxs)(l.Z.TabPane,{tab:"SMS Settings",children:[(0,i.jsx)("h4",{className:"text-center font-semibold underline",children:"SMS Settings Page"}),(0,i.jsx)("p",{className:"text-base",children:"For additional security purposes, we are using the Twilio messaging service. So company owner need to activate Twilio service after activated Twilio service he will get Twilio number, Twilio Auth Token, and Twilio Account SID. He can use the service only by providing them in the fields below."}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/marketing/sms-setting.png",alt:""}),(0,i.jsx)("p",{className:"text-base mt-4",children:"When the admin activates Twilio, all the users of his website will receive a code as a message on their phone after login and after verifying with the code, they can see the dashboard."}),(0,i.jsx)("p",{className:"bg-[#FFA525] text-white text-lg text-center py-4 rounded-lg font-semibold mt-5",children:"*Twilio is a paid service, the owner has to pay for this service."}),(0,i.jsx)("p",{className:"border-2 !border-[#FFA525] text-[#FFA525] text-lg text-center py-4 rounded-lg font-semibold mt-5",children:"If the admin does not want to use this service, he/she may not fill out this form, in that case, they can log in to the dashboard after giving the email password."})]},"6"),(0,i.jsxs)(l.Z.TabPane,{tab:"Whatsapp Settings",children:[(0,i.jsx)("h4",{className:"text-center font-semibold underline",children:"Whatsapp Settings Page"}),(0,i.jsx)("p",{className:"text-base",children:"For additional security purposes, we are using the Twilio Whatsapp service. So company owner need to activate Twilio service after activated Twilio service he will get Twilio Whatsapp number, Twilio Whatsapp Auth Token, and Twilio Whatsapp Account SID. He can use the service only by providing them in the fields below."}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/marketing/whatsapp-setting.png",alt:""}),(0,i.jsx)("p",{className:"text-base mt-4",children:"When the admin activates Twilio, all the users of his website will receive a code as a whatsapp message on their whatsapp account after login and after verifying with the code, they can see the dashboard."}),(0,i.jsx)("p",{className:"bg-[#FFA525] text-white text-lg text-center py-4 rounded-lg font-semibold mt-5",children:"*Twilio is a paid service, the owner has to pay for this service."}),(0,i.jsx)("p",{className:"border-2 !border-[#FFA525] text-[#FFA525] text-lg text-center py-4 rounded-lg font-semibold mt-5",children:"If the admin does not want to use this service, he/she may not fill out this form, in that case, they can log in to the dashboard after giving the email password."})]},"7"),(0,i.jsxs)(l.Z.TabPane,{tab:"Email Settings",children:[(0,i.jsx)("h4",{className:"text-center font-semibold underline",children:"Email Configuration"}),(0,i.jsxs)("div",{className:"my-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"SendGrid SMTP"}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsx)("p",{className:"text-base",children:"If you want to activate the SendGrid email service, please contact support."}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/marketing/send-grid-email.png",alt:""})]})]}),(0,i.jsxs)("div",{className:"my-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"Gmail Provider"}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsx)("p",{className:"text-base",children:"This option is enabled by default. If you use Gmail, you can get this data in the Gmail service."}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/marketing/gmail-provider.png",alt:""})]})]}),(0,i.jsxs)("div",{className:"my-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"Other Email Provider"}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsx)("p",{className:"text-base",children:"You can use other email services by collecting data and filling out all the forms according to the form below. If you faces any problem, please contact support."}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/marketing/other-provider.png",alt:""})]})]}),(0,i.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"another page"}),(0,i.jsx)("ul",{className:"list-disc text-base",children:(0,i.jsx)("li",{children:(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"font-medium text-[#FFA525]",children:"Email Templates :"})," These template will be shown in the email options page."]})})})]},"8")]})})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,5236,3652,9774,2888,179],(function(){return s=3574,e(e.s=s);var s}));var s=e.O();_N_E=s}]);