(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9961],{7481:function(e,t,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/settings/sms",function(){return s(2411)}])},2411:function(e,t,s){"use strict";s.r(t);var n=s(5893);s(7294);t.default=function(){return(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:"SMS settings"}),(0,n.jsxs)("div",{className:"m-4",children:[(0,n.jsx)("p",{className:"text-[18px] font-semibold",children:"To send SMS from the system, SMS credential need to be full-fill ."}),(0,n.jsx)("ul",{className:"list-disc",children:(0,n.jsxs)("li",{className:"my-4",children:[(0,n.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Twillo Provider"}),"Twillo is a cloud-based email service that provides reliable transactional and marketing email delivery. It is a great option for sending SMS from your application.",(0,n.jsxs)("p",{className:"text-[18px] font-semibold text-green-500",children:["Go to"," ",(0,n.jsx)("a",{href:"https://www.twilio.com/",target:"_blank",rel:"noreferrer",className:"text-blue-500",children:"https://www.twilio.com/"})," ","and create an account."]}),(0,n.jsx)("p",{className:"text-[18px] font-semibold text-green-500",children:"After creating an account, you will get an API key. Copy the API key and paste it in the API key field."}),(0,n.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/wa/sms.png",alt:""})]})})]})]})}}},function(e){e.O(0,[9774,2888,179],(function(){return t=7481,e(e.s=t);var t}));var t=e.O();_N_E=t}]);