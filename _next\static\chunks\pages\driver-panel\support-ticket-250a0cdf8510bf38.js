(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5979],{4773:function(e,t,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/driver-panel/support-ticket",function(){return s(3292)}])},3401:function(e,t,s){"use strict";s.d(t,{O:function(){return i}});var a=s(5893),i=(s(7294),function(e){var t=e.heading;return(0,a.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] pb-2",children:t})})},2250:function(e,t,s){"use strict";var a=s(5893),i=(s(7294),s(3652)),n=s(3401);t.Z=function(){return(0,a.jsx)(i.Z,{children:(0,a.jsxs)("div",{className:"bg-yellow-50 bg-opacity-20 h-auto m-6 w-[90%] text-[16px] p-4 rounded",children:[(0,a.jsx)(n.O,{heading:"Support Ticket"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{children:"Users can seek for help with any issue regarding the service. From the support ticket page user can view the created tickets and their status if he/she have already created a ticket. User can also see a detail view of the ticket by clicking on the Action column icon. Besides the user can also create a new ticket by clicking the add ticket button"}),(0,a.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/user-panel/support-ticket.png",alt:"support-ticket"}),(0,a.jsx)("p",{className:"mt-3",children:"There can be three types of status:"}),(0,a.jsxs)("ul",{className:"list-disc",children:[(0,a.jsxs)("li",{children:[(0,a.jsx)("span",{className:"text-yellow-500 font-semi-bold",children:"Pending: "}),"The ticket is created but has not been answered yet"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("span",{className:"text-green-500 font-semi-bold",children:"Open: "}),"The ticket has been answered."]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("span",{className:"text-red-500 font-semi-bold",children:"Closed: "}),"The issue is solved."]})]}),(0,a.jsx)("p",{className:"mt-3",children:"To create a new ticket click on the Add Ticket button. It will open a modal. Fill out the necessary information and click Add Ticket."}),(0,a.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/user-panel/add-ticket.png",alt:"add-ticket"}),(0,a.jsx)("p",{className:"mt-3",children:"In the detailed view page, user will see a Interface where he/she can"}),(0,a.jsxs)("ul",{className:"list-disc",children:[(0,a.jsx)("li",{children:"have live chat with an agent. The user may have to wait till the agent makes the first response"}),(0,a.jsx)("li",{children:"Take notes"})]}),(0,a.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/user-panel/ticket-details.png",alt:"ticket-details"})]})]})})}},3292:function(e,t,s){"use strict";s.r(t);var a=s(5893),i=(s(7294),s(2250));t.default=function(){return(0,a.jsx)(i.Z,{})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return t=4773,e(e.s=t);var t}));var t=e.O();_N_E=t}]);