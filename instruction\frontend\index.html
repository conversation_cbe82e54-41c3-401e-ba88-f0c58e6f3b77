<!DOCTYPE html><html><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width"/><meta name="next-head-count" content="2"/><link rel="preload" href="/_next/static/css/ef96b46dffe113f8.css" as="style"/><link rel="stylesheet" href="/_next/static/css/ef96b46dffe113f8.css" data-n-g=""/><link rel="preload" href="/_next/static/css/b252059a5226308e.css" as="style"/><link rel="stylesheet" href="/_next/static/css/b252059a5226308e.css" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="/_next/static/chunks/polyfills-0d1b80a048d4787e.js"></script><script src="/_next/static/chunks/webpack-a146a8ef8f1e9d05.js" defer=""></script><script src="/_next/static/chunks/framework-dfb4b001ca8efcfd.js" defer=""></script><script src="/_next/static/chunks/main-9871903446ae15d0.js" defer=""></script><script src="/_next/static/chunks/pages/_app-e20749b88fa2b6ce.js" defer=""></script><script src="/_next/static/chunks/545f34e4-58e6c361435faccc.js" defer=""></script><script src="/_next/static/chunks/252f366e-161ecfe97aa6e9c8.js" defer=""></script><script src="/_next/static/chunks/1bfc9850-cc8e23b0b03aea37.js" defer=""></script><script src="/_next/static/chunks/d0c16330-2f029357ec810197.js" defer=""></script><script src="/_next/static/chunks/78e521c3-55ace6d7da1b56a2.js" defer=""></script><script src="/_next/static/chunks/0c428ae2-69d07e327e6c5fb9.js" defer=""></script><script src="/_next/static/chunks/d7eeaac4-1832f2a1afc75712.js" defer=""></script><script src="/_next/static/chunks/7f0c75c1-7deaac14b2e27f32.js" defer=""></script><script src="/_next/static/chunks/1a48c3c1-b8a20c70283aa10a.js" defer=""></script><script src="/_next/static/chunks/de71a805-a5f5461fe2964f6a.js" defer=""></script><script src="/_next/static/chunks/17007de1-c0b841d120bc28b4.js" defer=""></script><script src="/_next/static/chunks/d64684d8-c6eb9551cedbb51b.js" defer=""></script><script src="/_next/static/chunks/3617-4f6dc0a363892407.js" defer=""></script><script src="/_next/static/chunks/3652-4389ba2c8f38f852.js" defer=""></script><script src="/_next/static/chunks/pages/instruction/frontend-0419c3833a8e8f87.js" defer=""></script><script src="/_next/static/KDIus1qkIHYmvN8aySE_u/_buildManifest.js" defer=""></script><script src="/_next/static/KDIus1qkIHYmvN8aySE_u/_ssgManifest.js" defer=""></script></head><body><div id="__next"><div><h4 class="text-center border-b-[1px] py-2 border-black">Frontend Installation Process</h4><div class="m-4"><ul class="list-disc"><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Step 1</p>Open the folder first.<img class="h-[500px] w-auto mt-4" src="/images/front.png" alt=""/></li><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Step 2</p>Then you need to find the <span class=" text-red-400"> next.config.js</span> file and open it in your appropriate text editor.<img class="h-[500px] w-auto my-4" src="/images/f2.png" alt=""/><p>Because you need to change the necessary information of your website such as <span class=" text-red-400">BACKEND URL, the text is underlined in the image below</span>, in this case, you must be provided your personal backend domain or sub domain URL and save it.</p><img class="h-[500px] w-auto mt-4" src="/images/f3.png" alt=""/></li><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Step 3</p>Then you need to open your terminal or command prompt (mac and linus user) or windows user you have to follow the picture. Select the address bar and type <span class=" text-red-400">cmd</span><img class="h-[500px] w-auto my-4" src="/images/f5.png" alt=""/><p>Then the command prompt open. Type <span class=" text-red-400">yarn run buildexport</span> and hit enter</p><img class="h-auto w-auto my-4" src="/images/f6.png" alt=""/><p>After build successfully, you can see <span class=" text-red-400">Export successful</span> message and then you will get <span class=" text-red-400">out</span> folder</p><img class="h-auto w-auto my-4" src="/images/f7.png" alt=""/></li><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Step 4</p><p>Now zip the <span class=" text-red-400">out</span> folder and upload it to your server.</p><img class="h-auto w-auto my-4" src="/images/f8.png" alt=""/></li><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Step 5</p><p>Then you go to your server panel or cPanel, Select your <span class=" text-red-400">Document Root</span> and right click on your mouse.</p><img class="h-auto w-auto my-4" src="/images/f9.png" alt=""/><p class="text-[20px] text-red-500 font-bold">** Now you need to follow the backend installation process Step 2 - Step 10</p><p class="text-[20px] text-red-500 font-bold">Note: in Step 7 of backend installation process, in the domain name section please confirm that it will be the root domain (Not a subdomain). For example, your_main_domain.com</p></li><p class="border-[1px] border-green-500 text-center p-2 text-green-500 font-semibold text-[24px]">After successful completion of all the processes, your website will go live, visit your domain url, for example <span class="italic ">your_main_domain.com</span> </p><p class="text-right"><a class="lg:mr-6 hover:cursor-pointer font-[Montserrat]" href="mailto:<EMAIL>">***** <EMAIL> *****</a></p></ul></div></div></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{}},"page":"/instruction/frontend","query":{},"buildId":"KDIus1qkIHYmvN8aySE_u","nextExport":true,"autoExport":true,"isFallback":false,"scriptLoader":[]}</script></body></html>