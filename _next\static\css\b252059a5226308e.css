/*!
* metismenujs - v1.3.1
* A menu plugin
* https://github.com/onokumus/metismenujs#readme
*
* Made by <PERSON><PERSON> <<EMAIL>> (https://github.com/onokumus)
* Under MIT License
*/.metismenu .mm-collapse:not(.mm-show){display:none}.metismenu .mm-collapsing{position:relative;height:0;overflow:hidden;transition-timing-function:ease;transition-duration:.35s;transition-property:height,visibility}.metismenu .has-arrow{position:relative}.metismenu .has-arrow:after{position:absolute;content:"";width:.5em;height:.5em;border-width:0 0 1px 1px;border-style:solid;border-color:initial;inset-inline-end:1em;transform:rotate(45deg) translateY(-50%);transform-origin:top;top:50%;transition:all .3s ease-out}[dir=rtl] .metismenu .has-arrow:after{transform:rotate(-135deg) translateY(-50%)}.metismenu .has-arrow[aria-expanded=true]:after,.metismenu .mm-active>.has-arrow:after{transform:rotate(-45deg) translateY(-50%)}