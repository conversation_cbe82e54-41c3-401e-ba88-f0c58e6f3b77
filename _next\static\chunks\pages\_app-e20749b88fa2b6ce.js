(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2888],{1118:function(n,t,e){(window.__NEXT_P=window.__NEXT_P||[]).push(["/_app",function(){return e(2373)}])},2373:function(n,t,e){"use strict";function r(n,t,e){return t in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}e.r(t),e.d(t,{default:function(){return o}});var u=e(5893);e(4831),e(4278),e(5062),e(8322),e(2739),e(993);var o=function(n){var t=n.Component,e=n.pageProps;return(0,u.jsx)(t,function(n){for(var t=1;t<arguments.length;t++){var e=null!=arguments[t]?arguments[t]:{},u=Object.keys(e);"function"===typeof Object.getOwnPropertySymbols&&(u=u.concat(Object.getOwnPropertySymbols(e).filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})))),u.forEach((function(t){r(n,t,e[t])}))}return n}({},e))}},5062:function(){},993:function(){},2739:function(){},8322:function(){},4278:function(){},4831:function(){}},function(n){var t=function(t){return n(n.s=t)};n.O(0,[9774,179],(function(){return t(1118),t(387)}));var e=n.O();_N_E=e}]);