(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9991],{5738:function(e,n,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/driver-panel/earnings",function(){return a(98)}])},3401:function(e,n,a){"use strict";a.d(n,{O:function(){return s}});var r=a(5893),s=(a(7294),function(e){var n=e.heading;return(0,r.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] pb-2",children:n})})},98:function(e,n,a){"use strict";a.r(n);var r=a(5893),s=(a(7294),a(3652)),t=a(3401),i=a(1664),l=a.n(i);n.default=function(){return(0,r.jsx)(s.Z,{children:(0,r.jsxs)("div",{className:"bg-yellow-50 bg-opacity-20 h-auto m-6 w-[90%] text-[16px] p-4 rounded",children:[(0,r.jsx)(t.O,{heading:"Earnings"}),(0,r.jsx)("p",{children:"Earnings page shows a summary of the drivers' earning related information."}),(0,r.jsxs)("ul",{className:"list-disc",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("span",{className:"font-bold",children:"Total Earning: "}),"Total earnings by the driver till now"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("span",{className:"font-bold",children:"Total Withdraw: "}),"Total withdraw by the driver till now"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("span",{className:"font-bold",children:"Balance: "}),"Current balance available to withdraw"]})]}),(0,r.jsx)("p",{children:"The table that shows all the payments that has been added to the driver wallet"}),(0,r.jsxs)("p",{className:"mt-3",children:["Clicking on the Trip Id will redirect the user to the ",(0,r.jsx)(l(),{href:"/trip-details",children:(0,r.jsx)("a",{target:"blank",children:"trip-details"})})," page"]}),(0,r.jsx)("img",{className:"h-auto w-auto mx-auto mt-2",src:"/images/driver-panel/earnings.png",alt:""})]})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return n=5738,e(e.s=n);var n}));var n=e.O();_N_E=n}]);