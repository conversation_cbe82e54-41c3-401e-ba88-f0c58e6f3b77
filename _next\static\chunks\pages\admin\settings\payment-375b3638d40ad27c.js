(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5175],{7513:function(e,s,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/settings/payment",function(){return a(3318)}])},3318:function(e,s,a){"use strict";a.r(s);var t=a(5893);a(1664),a(7294),a(3652);s.default=function(){return(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:"Payment settings"}),(0,t.jsxs)("div",{className:"m-4",children:["payment settings will be here . First you need to select the payment gateway. Then you need to fill the credential of the payment gateway. we are providing 5 payment gateway option.Choose the payment gateway that you want to use.",(0,t.jsxs)("ul",{className:"list-disc",children:[(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"SSLCOMMERZ"}),(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/payment/ssl.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Stripe"}),(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/payment/stripe.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Paypal"}),(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/payment/paypal.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"RazorPay"}),(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/payment/rz.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Mollie"}),(0,t.jsx)("img",{className:" w-auto mt-4",src:"/images/setting/payment/moll.png",alt:""})]})]})]})]})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return s=7513,e(e.s=s);var s}));var s=e.O();_N_E=s}]);