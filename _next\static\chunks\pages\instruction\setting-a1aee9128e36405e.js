(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2202],{4013:function(e,s,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/instruction/setting",function(){return a(9373)}])},9373:function(e,s,a){"use strict";a.r(s);var r=a(5893),i=a(1664),t=a.n(i),n=a(1163),o=(a(7294),a(3652));s.default=function(){var e=(0,n.useRouter)();return(0,r.jsx)(o.Z,{children:(0,r.jsxs)("div",{className:"bg-gray-50 h-auto m-6 w-[90%] text-[16px] p-4",children:[(0,r.jsx)("h4",{className:"text-center",children:"Env File Setup Information"}),(0,r.jsxs)("div",{className:"my-4",children:[(0,r.jsx)("h5",{className:"border-b-[1px] border-black",children:".env File Setup Information"}),(0,r.jsxs)("div",{className:"m-4",children:[(0,r.jsxs)("p",{children:["Now hit your domain on browser like, ",(0,r.jsx)("span",{className:"text-red-500 font-bold",children:"your_main_domain.com"})," first, the following page will be appeared. Now follow the below steps."]}),(0,r.jsx)("img",{className:"h-auto w-auto",src:"/images/setting.png",alt:""})]})]}),(0,r.jsxs)("div",{className:"my-4",children:[(0,r.jsx)("h5",{className:"border-b-[1px] border-black capitalize",children:" Admin creation"}),(0,r.jsxs)("div",{className:"m-4",children:[(0,r.jsx)("p",{children:"In this section, the admin or company owner has to provide the basic information like username, email, phone number and password to activate the website."}),(0,r.jsx)("img",{className:"h-auto w-auto",src:"/images/s1.png",alt:""})]})]}),(0,r.jsxs)("div",{className:"my-4",children:[(0,r.jsx)("h5",{className:"border-b-[1px] border-black capitalize",children:"Database setup "}),(0,r.jsx)("p",{onClick:function(){e.push("/instruction/setting/db/")},className:"text-red-500 w-[100px] text-center p-2 rounded border-2 border-gray-100 font-medium cursor-pointer hover:border-2 hover:border-red-500",children:"Click here"})]}),(0,r.jsxs)("div",{className:"my-4",children:[(0,r.jsx)("h5",{className:"border-b-[1px] border-black capitalize",children:"AWS Bucket Information"}),(0,r.jsxs)("div",{className:"m-4",children:[(0,r.jsxs)("p",{children:["Please fill out proper AWS bucket information. ",(0,r.jsx)(t(),{href:"https://docs.aws.amazon.com/AmazonS3/latest/userguide/configuring-bucket-key.html",children:(0,r.jsx)("a",{target:"_blank",rel:"opener",children:"Click Here for proper instructions"})})]}),(0,r.jsx)("img",{className:"h-auto w-auto",src:"/images/s4.png",alt:""})]})]}),(0,r.jsx)("div",{className:"my-4 shadow p-4",children:(0,r.jsx)("p",{className:"text-red-500 font-semibold mb-0",children:"After completion this process you have to restart the server."})}),(0,r.jsxs)("div",{className:"my-4",children:[(0,r.jsx)("h5",{className:"border-b-[1px] border-black capitalize",children:"Server Restart"}),(0,r.jsxs)("div",{className:"m-4",children:[(0,r.jsx)("p",{children:"Go to your cPanel or server panel. Click your backend folder."}),(0,r.jsx)("img",{className:"h-auto w-auto",src:"/images/restart1.png",alt:""})]}),(0,r.jsxs)("div",{className:"m-4",children:[(0,r.jsx)("p",{children:"Then you will get this kind of pop up, follow the instructions in the picture."}),(0,r.jsx)("img",{className:"h-auto w-auto",src:"/images/restart2.png",alt:""})]}),(0,r.jsxs)("div",{className:"m-4",children:[(0,r.jsx)("p",{children:"Then click on the Restart button."}),(0,r.jsx)("img",{className:"h-auto w-auto",src:"/images/restart3.png",alt:""})]}),(0,r.jsx)("div",{className:"my-4 shadow p-4",children:(0,r.jsx)("p",{className:"text-red-500 font-semibold mb-0",children:"Then go to your website and reload this page. Then you can see your home page."})})]})]})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return s=4013,e(e.s=s);var s}));var s=e.O();_N_E=s}]);