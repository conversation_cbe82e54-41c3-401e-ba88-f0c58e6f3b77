(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5893],{8627:function(e,t,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/driver-panel/trip-now",function(){return s(4124)}])},3401:function(e,t,s){"use strict";s.d(t,{O:function(){return a}});var r=s(5893),a=(s(7294),function(e){var t=e.heading;return(0,r.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] pb-2",children:t})})},4124:function(e,t,s){"use strict";s.r(t);var r=s(5893),a=(s(7294),s(3652)),n=s(3401);s(1664);t.default=function(){return(0,r.jsx)(a.Z,{children:(0,r.jsxs)("div",{className:"bg-yellow-50 bg-opacity-20 h-auto m-6 w-[90%] text-[16px] p-4 rounded",children:[(0,r.jsx)(n.O,{heading:"Trip Now"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{children:"The driver can manage a ride from here: You must have to online by clicking active button to get ride request. Otherwise if you are in offline, you will not get any ride request from user."}),(0,r.jsx)("img",{className:"h-auto w-auto mx-auto mt-2 shadow-sm",src:"/images/driver-panel/trip-now.png",alt:""}),(0,r.jsxs)("div",{className:"space-y-16 mt-5",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h5",{className:"border-b w-1/2 pb-1 border-[#FFA525] text-[#FFA525]",children:"Step - 1"}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-bold",children:"Ride Request: "})," When a user request to you for a ride then you can see the user information, ride distance and destination. You can decline or accept the ride from here."]}),(0,r.jsx)("img",{className:"h-auto w-auto mx-auto mt-2 shadow-sm",src:"/images/driver-panel/request.png",alt:""})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h5",{className:"border-b w-1/2 pb-1 border-[#FFA525] text-[#FFA525]",children:"Step - 2"}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-bold",children:"Ride Status: "})," When you pickup the user then you have to click the"," ",(0,r.jsx)("span",{className:"font-medium text-[#FFA525]",children:"Click here to pickup the user"})," ","button ."]}),(0,r.jsx)("img",{className:"h-auto w-auto mx-auto mt-2 shadow-sm",src:"/images/driver-panel/ride-status.png",alt:""})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h5",{className:"border-b w-1/2 pb-1 border-[#FFA525] text-[#FFA525]",children:"Step - 3"}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-bold",children:"Start Trip: "})," After pickup the user, you hav to click the"," ",(0,r.jsx)("span",{className:"font-medium text-[#FFA525]",children:"Click here to start the trip"})," ","button."]}),(0,r.jsx)("img",{className:"h-auto w-auto mx-auto mt-2 shadow-sm",src:"/images/driver-panel/start-trip.png",alt:""})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h5",{className:"border-b w-1/2 pb-1 border-[#FFA525] text-[#FFA525]",children:"Step - 4"}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-bold",children:"Complete Trip: "})," Wow!! The trip has been completed. Now you have to click on the"," ",(0,r.jsx)("span",{className:"font-medium text-[#FFA525]",children:"Click here to complete the trip"})," ","button."]}),(0,r.jsx)("img",{className:"h-auto w-auto mx-auto mt-2 shadow-sm",src:"/images/driver-panel/complete-trip.png",alt:""})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h5",{className:"border-b w-1/2 pb-1 border-[#FFA525] text-[#FFA525]",children:"Step - 5"}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-bold",children:"Getting Payment: "})," After successfully completed the trip you have to wait some moment until the user complete payment."]}),(0,r.jsx)("img",{className:"h-auto w-auto mx-auto mt-2 shadow-sm",src:"/images/driver-panel/payment-wait.png",alt:""})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h5",{className:"border-b w-1/2 pb-1 border-[#FFA525] text-[#FFA525]",children:"Step - 6"}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-bold",children:"Payment Success: "})," You can see your earning from earning table or trip history after successfully complete the payment with actual amount."]})]})]})]})]})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return t=8627,e(e.s=t);var t}));var t=e.O();_N_E=t}]);