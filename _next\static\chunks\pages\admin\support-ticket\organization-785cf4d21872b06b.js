(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3590],{483:function(e,s,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/support-ticket/organization",function(){return a(7840)}])},7840:function(e,s,a){"use strict";a.r(s);var i=a(5893);a(7294);s.default=function(){return(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:"Organization "}),(0,i.jsx)("div",{className:"m-4",children:(0,i.jsx)("ul",{className:"list-disc",children:(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Descriptions"}),"Organization contains the list of all the organizations that are created by the admin. Admin can create, edit, and delete organizations. each organization contains the following information:",(0,i.jsxs)("ul",{className:"list-disc ml-4",children:[(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Name"}),"Name of the organization."]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Email"}),"Email of the organization."]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Phone"}),"Phone number of the organization."]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Address"}),"Address of the organization."]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"City"}),"City of the organization."]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Postal Code"}),"Postal code of the organization."]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Country"}),"Country of the organization."]})]}),(0,i.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/support_ticket/organization.png",alt:""})]})})})]})}}},function(e){e.O(0,[9774,2888,179],(function(){return s=483,e(e.s=s);var s}));var s=e.O();_N_E=s}]);