(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8569],{8028:function(e,s,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/instruction/backend",function(){return a(5433)}])},5433:function(e,s,a){"use strict";a.r(s);var t=a(5893),l=(a(7294),a(3652));s.default=function(){return(0,t.jsx)(l.Z,{children:(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:"Backend Installation Process"}),(0,t.jsxs)("div",{className:"m-4",children:[(0,t.jsxs)("ul",{className:"list-disc",children:[(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 1"}),"First of all, you need to deploy the backend in your Domain or Subdomain. ",(0,t.jsxs)("span",{className:"italic",children:[" But we recommend subdomain actually. For example, ",(0,t.jsx)("span",{className:" text-red-400",children:"<EMAIL>."})]}),(0,t.jsx)("br",{})," Now you need to zip the taxstick-backend file.",(0,t.jsx)("img",{className:"h-auto w-auto mt-4",src:"/images/i4.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 2"}),"Then you have to go to your file manager of your server panel or cPanel, then upload the taxstick-backend.zip file.",(0,t.jsx)("img",{className:"h-auto w-auto mt-4",src:"/images/b1.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 3"}),"Then ",(0,t.jsx)("span",{className:"italic text-red-400",children:"Unzip"})," the file,",(0,t.jsx)("img",{className:"h-auto w-auto mt-4",src:"/images/b6.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 4"}),"Open your terminal and enter",(0,t.jsx)("span",{className:"italic text-red-400 font-medium",children:" sudo yarn"})," command and hit enter,",(0,t.jsx)("img",{className:"h-auto w-auto mt-4",src:"/images/sudo.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 5"}),"Now you can see the uploaded files And You can see ",(0,t.jsx)("span",{className:"text-green-500",children:"node_modules."}),(0,t.jsx)("img",{className:"h-auto w-auto mt-4",src:"/images/b8.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 6"}),"Then you can find the ",(0,t.jsx)("span",{className:"italic text-red-400",children:" Website"})," button on the sidebar and click on it.",(0,t.jsx)("img",{className:"h-auto w-auto mt-4",src:"/images/b9.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 7"}),"Then you can find the ",(0,t.jsx)("span",{className:"italic text-red-400",children:" Node Project"})," button on the top and click on it. Then click ",(0,t.jsx)("span",{className:"italic text-red-400",children:" add node project"})," button",(0,t.jsx)("img",{className:"h-auto w-auto mt-4",src:"/images/b10.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 8"}),"Then click the pointed button,",(0,t.jsxs)("ul",{className:"list-disc",children:[(0,t.jsx)("li",{children:"to select your folder path,"}),(0,t.jsx)("li",{children:"fill up the Name, (do not give line space in the name, use underscore)"}),(0,t.jsx)("li",{children:"select Run opt,"}),(0,t.jsx)("li",{children:"select the port,"}),(0,t.jsx)("li",{children:"select node version, "}),(0,t.jsx)("li",{children:"and give your domain name"})]}),"Then click on confirm button. Make sure all information is correct.",(0,t.jsx)("img",{className:"h-auto w-auto mt-4",src:"/images/b11.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 9"}),"Then click the file and right-click on your mouse, then you can get a new window",(0,t.jsx)("img",{className:"h-auto w-auto mt-4",src:"/images/b12.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 10"}),"Then Click SSL, then select two checkboxes and apply them",(0,t.jsx)("img",{className:"h-auto w-auto mt-4",src:"/images/b15.png",alt:""})]}),(0,t.jsxs)("li",{className:"my-4",children:[(0,t.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Step 11"}),"Then you can see on the top of the window a Force HTTPS switch, please click it on",(0,t.jsx)("img",{className:"h-auto w-auto mt-4",src:"/images/b14.png",alt:""})]})]}),(0,t.jsxs)("h6",{className:"text-red-600 p-4 border-[1px] border-red-600 text-center",children:["Now your backend project is ready to use. Please copy your subdomain URL to run your frontend project. For example, ",(0,t.jsx)("span",{className:" italic",children:"backend.your_main_domain.com"}),"."]})]})]})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return s=8028,e(e.s=s);var s}));var s=e.O();_N_E=s}]);