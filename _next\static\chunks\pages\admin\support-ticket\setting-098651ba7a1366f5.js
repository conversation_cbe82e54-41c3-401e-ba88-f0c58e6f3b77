(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[681],{3008:function(t,e,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/support-ticket/setting",function(){return s(5922)}])},5922:function(t,e,s){"use strict";s.r(e);var i=s(5893);s(7294);e.default=function(){return(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:"Support-ticket Setting"}),(0,i.jsx)("div",{className:"m-4",children:(0,i.jsx)("ul",{className:"list-disc",children:(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Descriptions"}),"Support-ticket Setting contains the list of all the settings.",(0,i.jsxs)("ul",{className:"list-disc",children:[(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Department Setting"}),"Department Setting contains the list of all the departments that are created by the admin. Admin can create, edit, and delete departments.",(0,i.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/support_ticket/setting/setting1.png",alt:""})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Category Setting"}),"Category Setting contains the list of all the categories that are created by the admin. Admin can create, edit, and delete categories.",(0,i.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/support_ticket/setting/setting2.png",alt:""})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Type Setting"}),"Type Setting contains the list of all the Type that are created by the admin. Admin can create, edit, and delete type.",(0,i.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/support_ticket/setting/setting3.png",alt:""})]}),(0,i.jsxs)("li",{className:"my-4",children:[(0,i.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Priorities Setting"}),"Priorities Setting contains the list of all the priorities that are created by the admin. Admin can create, edit, and delete triorities.",(0,i.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/support_ticket/setting/setting1.png",alt:""})]})]})]})})})]})}}},function(t){t.O(0,[9774,2888,179],(function(){return e=3008,t(t.s=e);var e}));var e=t.O();_N_E=e}]);