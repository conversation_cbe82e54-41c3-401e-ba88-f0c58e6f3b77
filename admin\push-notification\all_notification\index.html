<!DOCTYPE html><html><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width"/><meta name="next-head-count" content="2"/><link rel="preload" href="/_next/static/css/ef96b46dffe113f8.css" as="style"/><link rel="stylesheet" href="/_next/static/css/ef96b46dffe113f8.css" data-n-g=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="/_next/static/chunks/polyfills-0d1b80a048d4787e.js"></script><script src="/_next/static/chunks/webpack-a146a8ef8f1e9d05.js" defer=""></script><script src="/_next/static/chunks/framework-dfb4b001ca8efcfd.js" defer=""></script><script src="/_next/static/chunks/main-9871903446ae15d0.js" defer=""></script><script src="/_next/static/chunks/pages/_app-e20749b88fa2b6ce.js" defer=""></script><script src="/_next/static/chunks/pages/admin/push-notification/all_notification-3bbb7ae878215387.js" defer=""></script><script src="/_next/static/KDIus1qkIHYmvN8aySE_u/_buildManifest.js" defer=""></script><script src="/_next/static/KDIus1qkIHYmvN8aySE_u/_ssgManifest.js" defer=""></script></head><body><div id="__next"><div><h4 class="text-center border-b-[1px] py-2 border-black">All Push-notifications<!-- --> </h4><div class="m-4"><ul class="list-disc"><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Descriptions</p>All the push-notifications that are sent to the users will be displayed in this table.Push-notifications are sorted by the date and time they are sent. Table contains the following columns:<ul class="list-disc"><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Title</p><p>It is the title of the notification that is sent to the user.</p></li><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Body</p><p>It is the body of the notification that is sent to the user.</p></li><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Status</p><p>a notification&#x27;s status can be either success,scheduled or failed</p></li><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Date</p><p>It is the date and time when the notification is sent to the user.</p></li><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Group Name</p><p>It is the name of the group to which the notification is sent.if group name showed as &quot;...&quot;, that means it is send to all user or driver or users.<!-- --> </p></li></ul><img class="h-[500px] w-auto mt-4" src="/images/push_notifications/all.png" alt=""/></li></ul></div></div></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{}},"page":"/admin/push-notification/all_notification","query":{},"buildId":"KDIus1qkIHYmvN8aySE_u","nextExport":true,"autoExport":true,"isFallback":false,"scriptLoader":[]}</script></body></html>