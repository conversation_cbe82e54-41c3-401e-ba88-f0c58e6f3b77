(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6111],{5413:function(e,t,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/support-ticket",function(){return s(1707)}])},4377:function(e,t,s){"use strict";s.r(t);var a=s(5893);s(7294);t.default=function(){return(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:"Support-Ticket Agents "}),(0,a.jsx)("div",{className:"m-4",children:(0,a.jsxs)("ul",{className:"list-disc",children:[(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Descriptions"}),"Ticket-employee are created by the admin. Each ticket-employee  can reply to the ticket and can change the status of the ticket. Ticket will be automatically assigned to  ticket-employee  by  ticket department and category. This screen contains the list of all the agents that are created by the admin. Admin can create, edit, and delete agents.",(0,a.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/support_ticket/agent/agent.png",alt:""})]}),(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Add Employee"}),"To add a new Ticket employee, click on the ",(0,a.jsx)("span",{className:"font-semibold",children:"Add Employee"})," button. Enter the name, email, password, and role in the respective fields. Click on the ",(0,a.jsx)("span",{className:"font-semibold",children:"Save"})," button to save the employee.",(0,a.jsx)("p",{className:"text-orange-600 font-semibold",children:"             **               Note: Select Department and Designation for the employee must be equal to ticket & support **"}),(0,a.jsx)("img",{className:"w-auto mt-4",src:"/images/support_ticket/agent/addagent.png",alt:""})]})]})})]})}},1707:function(e,t,s){"use strict";s.r(t);var a=s(5893),i=(s(7294),s(3652)),l=s(5236),n=s(3386),c=s(7814),r=s(7840),o=s(4377),d=s(5922),m=[{title:"Ticket",page:(0,a.jsx)(n.default,{})},{title:"Knowledge Base",page:(0,a.jsx)(c.default,{})},{title:"Organization",page:(0,a.jsx)(r.default,{})},{title:"Agents",page:(0,a.jsx)(o.default,{})},{title:"Settings",page:(0,a.jsx)(d.default,{})}];t.default=function(){return(0,a.jsx)(i.Z,{children:(0,a.jsx)("div",{className:"bg-yellow-50 bg-opacity-20 h-auto m-6 w-[90%] text-[16px] p-4 rounded",children:(0,a.jsx)(l.Z,{defaultActiveKey:"1",centered:!0,children:m.map((function(e,t){return(0,a.jsx)(l.Z.TabPane,{tab:e.title,children:e.page},t+1)}))})})})}},7814:function(e,t,s){"use strict";s.r(t);var a=s(5893);s(7294);t.default=function(){return(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:"Knowledge Base "}),(0,a.jsx)("div",{className:"m-4",children:(0,a.jsxs)("ul",{className:"list-disc",children:[(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Descriptions"}),"Knowledge base is a collection of Question & Answer that are created by the admin. Each article contains a Question and Answer . Admin can create, edit, and delete articles. Admin can also create, edit, and delete",(0,a.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/support_ticket/knwledgebase/kb.png",alt:""})]}),(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Add Knowledge-base"}),"To add a new knowledge base, click on the ",(0,a.jsx)("span",{className:"font-semibold",children:"Add Knowledge-base"})," button. Enter the Question and Answer in the respective fields. Click on the ",(0,a.jsx)("span",{className:"font-semibold",children:"Save"})," button to save the knowledge base.",(0,a.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/support_ticket/knwledgebase/addkb.png",alt:""})]}),(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Add Knowledge-base"}),"There is edit and delete button on the right side of each knowledge base. Click on the ",(0,a.jsx)("span",{className:"font-semibold",children:"Edit"})," button to edit the knowledge base.",(0,a.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/support_ticket/knwledgebase/editkb.png",alt:""})]})]})})]})}},7840:function(e,t,s){"use strict";s.r(t);var a=s(5893);s(7294);t.default=function(){return(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:"Organization "}),(0,a.jsx)("div",{className:"m-4",children:(0,a.jsx)("ul",{className:"list-disc",children:(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Descriptions"}),"Organization contains the list of all the organizations that are created by the admin. Admin can create, edit, and delete organizations. each organization contains the following information:",(0,a.jsxs)("ul",{className:"list-disc ml-4",children:[(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Name"}),"Name of the organization."]}),(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Email"}),"Email of the organization."]}),(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Phone"}),"Phone number of the organization."]}),(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Address"}),"Address of the organization."]}),(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"City"}),"City of the organization."]}),(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Postal Code"}),"Postal code of the organization."]}),(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Country"}),"Country of the organization."]})]}),(0,a.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/support_ticket/organization.png",alt:""})]})})})]})}},5922:function(e,t,s){"use strict";s.r(t);var a=s(5893);s(7294);t.default=function(){return(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:"Support-ticket Setting"}),(0,a.jsx)("div",{className:"m-4",children:(0,a.jsx)("ul",{className:"list-disc",children:(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Descriptions"}),"Support-ticket Setting contains the list of all the settings.",(0,a.jsxs)("ul",{className:"list-disc",children:[(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Department Setting"}),"Department Setting contains the list of all the departments that are created by the admin. Admin can create, edit, and delete departments.",(0,a.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/support_ticket/setting/setting1.png",alt:""})]}),(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Category Setting"}),"Category Setting contains the list of all the categories that are created by the admin. Admin can create, edit, and delete categories.",(0,a.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/support_ticket/setting/setting2.png",alt:""})]}),(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Type Setting"}),"Type Setting contains the list of all the Type that are created by the admin. Admin can create, edit, and delete type.",(0,a.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/support_ticket/setting/setting3.png",alt:""})]}),(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Priorities Setting"}),"Priorities Setting contains the list of all the priorities that are created by the admin. Admin can create, edit, and delete triorities.",(0,a.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/support_ticket/setting/setting1.png",alt:""})]})]})]})})})]})}},3386:function(e,t,s){"use strict";s.r(t);var a=s(5893);s(7294);t.default=function(){return(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-center border-b-[1px] py-2 border-black",children:" Support Tickets "}),(0,a.jsx)("div",{className:"m-4",children:(0,a.jsx)("ul",{className:"list-disc",children:(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Detail page"}),"This is the detail page of the ticket. Here you can see the details of the ticket.",(0,a.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/support_ticket/ticket/1ticketpage.png",alt:""}),(0,a.jsxs)("ul",{className:"list-disc",children:[(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Search & Filter"}),"You can search and filter the tickets by using the search bar and filter options.",(0,a.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/support_ticket/ticket/2searchandfilter.png",alt:""})]}),(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Add Ticket"}),"You can add a new ticket by clicking on the add button. This will open a modal where you can add the details of the ticket.",(0,a.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/support_ticket/ticket/3addticket.png",alt:""})]}),(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Ticket Details"}),"You can see the details of the ticket in the detail page. You can also see the Notes and Files of the ticket.",(0,a.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/support_ticket/ticket/4detailspage.png",alt:""})]}),(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:" Details page Functionality"}),"Details page has the following functionality:",(0,a.jsxs)("ul",{className:"list-disc",children:[(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Reply to ticket"}),"You can reply to the ticket . Type your reply in the text area and click on the send button."]}),(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"Add Note"}),"You can add a note to the ticket. Click on the add note button and type your note in the text area."]}),(0,a.jsxs)("li",{className:"my-4",children:[(0,a.jsx)("p",{className:"border-b-[1px] border-black font-semibold text-[18px]",children:"CLose a ticket "}),"Only admin & employee can close a ticket. Click on the close button to close the ticket.",(0,a.jsx)("img",{className:"h-[500px] w-auto mt-4",src:"/images/support_ticket/ticket/5details%20page%20funtionality.png",alt:""})]})]})]})]})]})})})]})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,5236,3652,9774,2888,179],(function(){return t=5413,e(e.s=t);var t}));var t=e.O();_N_E=t}]);