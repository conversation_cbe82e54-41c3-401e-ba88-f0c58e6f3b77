(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6827],{2722:function(e,a,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/driverManagement",function(){return s(3111)}])},3111:function(e,a,s){"use strict";s.r(a);var i=s(5893),t=(s(7294),s(5236)),n=s(3652),r=s(1163);a.default=function(){var e=(0,r.useRouter)();return(0,i.jsx)(n.Z,{children:(0,i.jsx)("div",{className:"bg-yellow-50 bg-opacity-20 h-auto m-6 w-[90%] text-[16px] p-4 rounded",children:(0,i.jsxs)(t.Z,{defaultActiveKey:"1",centered:!0,children:[(0,i.jsxs)(t.<PERSON><PERSON>,{tab:"Driver List",children:[(0,i.jsx)("p",{className:"text-base mt-2",children:"On this page, the admin can view all driver\u2019s accounts and information. The admin can update and delete any driver using the action buttons."}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/driver-management/driver-list.png",alt:""}),(0,i.jsxs)("div",{className:"my-6",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] pb-2",children:"Ratings"}),(0,i.jsx)("p",{className:"text-base mt-2",children:"Here, the admin can view driver ratings that were given by users after completing a trip. The admin can manage it by making it active, inactive, or deleting it."}),(0,i.jsx)("img",{className:"h-auto w-auto mx-auto mt-2 shadow-sm",src:"/images/admin/driver-management/ratings.png",alt:""})]})]},"1"),(0,i.jsxs)(t.Z.TabPane,{tab:"Vehicle List",children:[(0,i.jsx)("p",{className:"text-base mt-2",children:"On this page, the admin can view all driver\u2019s vehicle list. The administrator can also view document details and update or delete any vehicle using the action buttons."}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/driver-management/vehicle-list.png",alt:""}),(0,i.jsxs)("div",{className:"my-6",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] pb-2",children:"Update Vehicle"}),(0,i.jsx)("p",{className:"text-base mt-2",children:"Here, the admin can update specific vehicle information and also update driver documents by clicking the click here button in the below."}),(0,i.jsx)("img",{className:"h-auto w-auto mx-auto mt-2 shadow-sm",src:"/images/admin/driver-management/vehicle-update.png",alt:""})]})]},"2"),(0,i.jsxs)(t.Z.TabPane,{tab:"Driver Earnings",children:[(0,i.jsx)("p",{className:"text-base mt-2",children:"On this page, the admin can view all drivers earnings lists. The administrator can also view earning details or delete any earning history using the action buttons."}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/driver-management/driver-earning.png",alt:""})]},"3"),(0,i.jsxs)(t.Z.TabPane,{tab:"Document Input",children:[(0,i.jsxs)("p",{className:"text-base mt-2",children:["On this page, the admin can view all registration form fields that will be shown on the landing page > earn with share > register page. The administrator can also add new form fields by clicking the ",(0,i.jsx)("span",{className:"text-[#FFA525] font-semibold",children:"Add Form Fields"})," button, and can also delete any form field using the action buttons on the right side."]}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/driver-management/document/form-fields.png",alt:""}),(0,i.jsx)("p",{className:"text-base underline font-semibold capitalize mt-4",children:"landing page > earn with share > register"}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/admin/driver-management/document/lan-regi.png",alt:""}),(0,i.jsxs)("div",{className:"my-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"Add Form Fields"}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsxs)("p",{className:"text-base",children:["When the admin clicks on ",(0,i.jsx)("span",{className:"text-[#FFA525] font-semibold",children:"Add Form Fields"})," button, then the admin will see a drawer. Now admin can add new fields from here."]}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm border mx-auto",src:"/images/admin/driver-management/document/add-new.png",alt:""}),(0,i.jsx)("p",{className:"text-lg font-medium mt-4 mb-0 underline",children:"Method of use:"}),(0,i.jsxs)("ul",{className:"list-disc text-base",children:[(0,i.jsxs)("li",{children:["First, you need to insert a name in Input Name filed.",(0,i.jsx)("img",{className:"h-auto w-auto m-2 border",src:"/images/admin/driver-management/document/input-name.png",alt:""})]}),(0,i.jsxs)("li",{children:["Then select input type.",(0,i.jsx)("img",{className:"h-auto w-auto m-2 border",src:"/images/admin/driver-management/document/input-type.png",alt:""}),(0,i.jsx)("p",{className:"mt-4 mb-0",children:"While defining the input field you need to follow some information below:"}),(0,i.jsx)("ul",{className:"list-disc",children:(0,i.jsxs)("li",{children:["We have included 6 types of field types in this list:",(0,i.jsxs)("ul",{className:"list-disc",children:[(0,i.jsx)("li",{children:"Text: It collects string values."}),(0,i.jsx)("li",{children:"Number: It collects number value."}),(0,i.jsx)("li",{children:"Password: It collects password."}),(0,i.jsx)("li",{children:"Textarea: Through this, a lot of data can be taken at once."}),(0,i.jsx)("li",{children:"Images: It collects images."}),(0,i.jsxs)("li",{children:["Terms and conditions: Additional links can be added within this field.",(0,i.jsx)("img",{className:"h-auto w-auto m-2 border",src:"/images/admin/driver-management/document/terms-condition.png",alt:""})]}),(0,i.jsxs)("li",{children:["Input Placeholder: You need to specify what messages you want to show the audience.",(0,i.jsx)("img",{className:"h-auto w-auto m-2 border",src:"/images/placeholder.png",alt:""})]}),(0,i.jsxs)("li",{children:["You can manage the field whether it is required or not by selecting Yes or No respectively.",(0,i.jsx)("img",{className:"h-auto w-auto m-2 border",src:"/images/admin/driver-management/document/required.png",alt:""})]}),(0,i.jsxs)("li",{children:["Status: You can also set the status of the field too. If the status is True then this field will be visible to users. If the status is set to False then it will not be visible to users.",(0,i.jsx)("img",{className:"h-auto w-auto m-2 border",src:"/images/admin/driver-management/document/status.png",alt:""})]})]})]})})]}),(0,i.jsxs)("li",{className:"my-4 bg-[#FFA525] p-4 flex items-center",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h5",{className:"inline text-white",children:"Terms and Conditions: "}),(0,i.jsx)("p",{className:"inline text-white",children:"It is very important to add a terms and conditions option to your driver registration form. Please follow the process."})]}),(0,i.jsx)("button",{className:"inline text-white mx-4 border-2 border-white rounded px-4 py-1 cursor-pointer font-bold hover:bg-white hover:!text-[#FFA525]",onClick:function(){e.push("driverManagement/termCon/")},children:"Click Here"})]})]})]})]})]},"4")]})})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,5236,3652,9774,2888,179],(function(){return a=2722,e(e.s=a);var a}));var a=e.O();_N_E=a}]);