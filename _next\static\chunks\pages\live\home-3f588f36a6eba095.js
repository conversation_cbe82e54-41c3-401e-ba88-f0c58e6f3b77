(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6448],{2662:function(e,s,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/live/home",function(){return a(5588)}])},5588:function(e,s,a){"use strict";a.r(s);var i=a(5893),c=(a(7294),a(3652));s.default=function(){return(0,i.jsx)(c.Z,{children:(0,i.jsxs)("div",{className:"bg-zinc-100 h-auto m-6 w-[90%] text-[16px] p-4",children:[(0,i.jsx)("h4",{className:"text-center font-semibold underline",children:"Home Page"}),(0,i.jsxs)("div",{className:"my-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"header and hero section"}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsx)("p",{className:"text-base",children:"In this section, anyone can see the hero section with a banner image; here, all the sections are dynamic. You can change it according to your requirements in the admin panel."}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/homepage/home/<USER>",alt:""})]})]}),(0,i.jsxs)("div",{className:"my-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"brand/partnership"}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsx)("p",{className:"text-base",children:"In this section, anyone can see the partnership/brand company logo as a slider; this section is also dynamic."}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/homepage/home/<USER>",alt:""})]})]}),(0,i.jsxs)("div",{className:"my-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"platform service"}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsx)("p",{className:"text-base",children:"In this section, anyone can see all the services provided by the platform and their descriptions, which are dynamic."}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/homepage/home/<USER>",alt:""})]})]}),(0,i.jsxs)("div",{className:"my-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"platform work"}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsx)("p",{className:"text-base",children:"In this section, three cards are displayed for your company's work process flow."}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/homepage/home/<USER>",alt:""})]})]}),(0,i.jsxs)("div",{className:"my-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"counter section"}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsx)("p",{className:"text-base",children:"In this section, there are four options to display some value, such as: app downloads, active riders, active users, and trips or orders saved. This section is also dynamic."}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/homepage/home/<USER>",alt:""})]})]}),(0,i.jsxs)("div",{className:"my-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"benefit section"}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsx)("p",{className:"text-base",children:"In this section, three cards are displayed for your company's benefit information in a dynamic way."}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/homepage/home/<USER>",alt:""})]})]}),(0,i.jsxs)("div",{className:"my-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"testmonial section"}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsx)("p",{className:"text-base",children:"This is the user feedback section. The admin can delete and control this."}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/homepage/home/<USER>",alt:""})]})]}),(0,i.jsxs)("div",{className:"my-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"latest blog section"}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsx)("p",{className:"text-base",children:"In this section, anyone can see the latest blog as a slider. The admin can create, update, or delete any blog in the admin panel."}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/homepage/home/<USER>",alt:""})]})]}),(0,i.jsxs)("div",{className:"my-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"newsletter"}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsx)("p",{className:"text-base",children:"In this section, there is an input form. Anyone can subscribe to your platform by submitting his/her email here."}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/homepage/home/<USER>",alt:""})]})]}),(0,i.jsxs)("div",{className:"my-4",children:[(0,i.jsx)("h5",{className:"border-b-[1px] border-[#FFA525] capitalize pb-2",children:"Footer And Copyright"}),(0,i.jsxs)("div",{className:"m-4",children:[(0,i.jsx)("p",{className:"text-base",children:"This is the footer and copyright section, which is totally dynamic. The admin can change the logo and any data in the admin panel."}),(0,i.jsx)("img",{className:"h-auto w-auto shadow-sm",src:"/images/homepage/home/<USER>",alt:""})]})]})]})})}}},function(e){e.O(0,[4980,1228,5445,8907,5937,2013,4617,955,3874,4090,2876,6556,3617,3652,9774,2888,179],(function(){return s=2662,e(e.s=s);var s}));var s=e.O();_N_E=s}]);