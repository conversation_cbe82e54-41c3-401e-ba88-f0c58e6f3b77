<!DOCTYPE html><html><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width"/><meta name="next-head-count" content="2"/><link rel="preload" href="/_next/static/css/ef96b46dffe113f8.css" as="style"/><link rel="stylesheet" href="/_next/static/css/ef96b46dffe113f8.css" data-n-g=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="/_next/static/chunks/polyfills-0d1b80a048d4787e.js"></script><script src="/_next/static/chunks/webpack-a146a8ef8f1e9d05.js" defer=""></script><script src="/_next/static/chunks/framework-dfb4b001ca8efcfd.js" defer=""></script><script src="/_next/static/chunks/main-9871903446ae15d0.js" defer=""></script><script src="/_next/static/chunks/pages/_app-e20749b88fa2b6ce.js" defer=""></script><script src="/_next/static/chunks/pages/admin/settings/push_notification-6dd7b03fd99065fc.js" defer=""></script><script src="/_next/static/KDIus1qkIHYmvN8aySE_u/_buildManifest.js" defer=""></script><script src="/_next/static/KDIus1qkIHYmvN8aySE_u/_ssgManifest.js" defer=""></script></head><body><div id="__next"><div><h4 class="text-center border-b-[1px] py-2 border-black">Push notification credentials</h4><div class="m-4"><ul class="list-disc"><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Give the json file </p>json file is collected from firebase console.<img class=" w-auto mt-4" src="/images/setting/pn/push.png" alt=""/></li><h5 class="border-b-[1px] border-black">Getting the json file from firebase console</h5><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Step 1: Go to firebase</p>Go to firebase console.Click add project.<img class=" w-auto mt-4" src="/images/setting/pn/gotofirebase.png" alt=""/></li><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Step 2: Enter project name</p>Enter project name and click continue.<img class=" w-auto mt-4" src="/images/setting/pn/name.png" alt=""/></li><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Step 3: Chose platform</p>Chose platform in which you want to send push-notification.<img class=" w-auto mt-4" src="/images/setting/pn/iosand.png" alt=""/></li><li class="my-4"><p class="border-b-[1px] border-black font-semibold text-[18px]">Step 4: Configuration</p>configure the project.Fill the required details.After filling the details collect the settings file and upload it .<img class=" w-auto mt-4" src="/images/setting/pn/config.png" alt=""/></li></ul></div></div></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{}},"page":"/admin/settings/push_notification","query":{},"buildId":"KDIus1qkIHYmvN8aySE_u","nextExport":true,"autoExport":true,"isFallback":false,"scriptLoader":[]}</script></body></html>